import { useState, useCallback } from "react";
import { BackendAdvertisement } from "../definitions/adverts.definitions";
import { PortalOption } from "../definitions/app.types";
import { apiClient } from "../utils/api-client";
import { processAdsInChunks, addAdsInChunks, fetchWithMultipleAttempts } from "../utils/ad-operations.util";
import { ConvertedAdItem } from "../utils/convert-to-cms-advert.util";

/**
 * Hook for advertisement operations
 */
export function useAdvertisementOperations(
  selectedPortal: PortalOption,
  token: string,
  environment: string,
  fetchedAds?: BackendAdvertisement[],
  setFetchedAds?: React.Dispatch<React.SetStateAction<BackendAdvertisement[]>>
) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Fetch advertisements from the backend
   */
  const fetchAds = useCallback(async (forceRefresh: boolean = false): Promise<BackendAdvertisement[]> => {
    if (!token) {
      throw new Error("Authentication token is required");
    }

    // If we already have ads and don't need to force refresh, return them
    if (!forceRefresh && fetchedAds && fetchedAds.length > 0) {
      return fetchedAds;
    }

    try {
      setLoading(true);
      setError(null);

      const ads = await apiClient.fetchAds(
        token,
        selectedPortal.portal,
        selectedPortal.api,
        environment
      );

      if (setFetchedAds) {
        setFetchedAds(ads);
      }

      return ads;
    } catch (err) {
      const errorMessage = (err as Error).message || "Failed to fetch advertisements";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [token, selectedPortal, environment, fetchedAds, setFetchedAds]);

  /**
   * Clear all advertisements
   */
  const clearAds = useCallback(async (
    chunkSize: number,
    delay: number,
    onStatusUpdate?: (status: string) => void
  ): Promise<void> => {
    if (!token) {
      throw new Error("Authentication token is required");
    }

    try {
      setLoading(true);
      setError(null);

      // Always force a refresh to get the latest data
      const ads = await fetchAds(true);

      if (ads.length === 0) {
        if (onStatusUpdate) onStatusUpdate("No advertisements to clear.");
        return;
      }

      await processAdsInChunks(
        ads,
        chunkSize,
        delay,
        "delete",
        token,
        selectedPortal.portal,
        selectedPortal.api,
        environment,
        (chunkIndex, totalChunks, chunkSize) => {
          if (onStatusUpdate) onStatusUpdate(`Processing chunk ${chunkIndex} of ${totalChunks} (${chunkSize} ads)`);
        }
      );

      if (onStatusUpdate) onStatusUpdate("All ads deleted successfully.");
      if (setFetchedAds) setFetchedAds([]);
    } catch (err) {
      const errorMessage = (err as Error).message || "Failed to clear advertisements";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [token, selectedPortal, environment, fetchAds, setFetchedAds]);

  /**
   * Add advertisements
   */
  const addAds = useCallback(async (
    localAds: ConvertedAdItem[],
    chunkSize: number,
    delay: number,
    onStatusUpdate?: (status: string) => void,
    onFilterResults?: (total: number, filtered: number) => void
  ): Promise<void> => {
    if (!token) {
      throw new Error("Authentication token is required");
    }

    try {
      setLoading(true);
      setError(null);

      // Always force a refresh to get the latest data
      const serverAds = await fetchAds(true);

      await addAdsInChunks(
        localAds,
        chunkSize,
        delay,
        token,
        selectedPortal.portal,
        selectedPortal.api,
        environment,
        serverAds,
        (chunkIndex, totalChunks, chunkSize) => {
          if (onStatusUpdate) onStatusUpdate(`Adding chunk ${chunkIndex} of ${totalChunks} (${chunkSize} ads)`);
        },
        (delayTime) => {
          if (onStatusUpdate) onStatusUpdate(`Waiting ${delayTime/1000} seconds before next chunk...`);
        },
        onFilterResults
      );

      if (onStatusUpdate) onStatusUpdate("Ads added successfully.");
    } catch (err) {
      const errorMessage = (err as Error).message || "Failed to add advertisements";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [token, selectedPortal, environment, fetchAds]);

  /**
   * Activate advertisements
   */
  const activateAds = useCallback(async (
    chunkSize: number,
    delay: number,
    onStatusUpdate?: (status: string) => void
  ): Promise<void> => {
    if (!token) {
      throw new Error("Authentication token is required");
    }

    try {
      setLoading(true);
      setError(null);

      // Always force a refresh to get the latest data
      const ads = await fetchAds(true);

      if (ads.length === 0) {
        if (onStatusUpdate) onStatusUpdate("No advertisements to activate.");
        return;
      }

      // Filter out already active ads
      const inactiveAds = ads.filter(ad => !ad.isActive);

      if (inactiveAds.length === 0) {
        if (onStatusUpdate) onStatusUpdate("All advertisements are already active.");
        return;
      }

      await processAdsInChunks(
        inactiveAds,
        chunkSize,
        delay,
        "activate",
        token,
        selectedPortal.portal,
        selectedPortal.api,
        environment,
        (chunkIndex, totalChunks, chunkSize) => {
          if (onStatusUpdate) onStatusUpdate(`Activating chunk ${chunkIndex} of ${totalChunks} (${chunkSize} ads)`);
        }
      );

      if (onStatusUpdate) onStatusUpdate("All ads activated successfully.");
    } catch (err) {
      const errorMessage = (err as Error).message || "Failed to activate advertisements";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [token, selectedPortal, environment, fetchAds]);

  /**
   * Fetch with multiple attempts
   * Always forces a refresh to ensure we get the latest data
   */
  const fetchWithRetry = useCallback(async (
    attempts: number = 3,
    delayBetweenAttempts: number = 2000,
    onAttemptStart?: (attempt: number, totalAttempts: number) => void,
    onDelayStart?: (delayMs: number) => void
  ): Promise<BackendAdvertisement[]> => {
    // Directly use fetchAds - fetchWithMultipleAttempts will call it with forceRefresh=true
    return fetchWithMultipleAttempts(
      fetchAds,
      attempts,
      delayBetweenAttempts,
      onAttemptStart,
      onDelayStart
    );
  }, [fetchAds]);

  return {
    loading,
    error,
    fetchAds,
    clearAds,
    addAds,
    activateAds,
    fetchWithRetry
  };
}
