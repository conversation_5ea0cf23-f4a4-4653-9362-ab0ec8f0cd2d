import { useState, useEffect, useCallback } from "react";
import { BackendAdvertisement } from "../definitions/adverts.definitions";
import { PortalOption } from "../definitions/app.types";

export function useAdvertisements(
  selectedPortal: PortalOption,
  authToken: string,
  environment: string
) {
  const [serverAds, setServerAds] = useState<BackendAdvertisement[]>([]);
  const [localAds, setLocalAds] = useState<BackendAdvertisement[]>([]);
  const [localAdsExist, setLocalAdsExist] = useState<boolean>(false);
  const [backendFetched, setBackendFetched] = useState<boolean>(false);

  // Fetch local ads
  const fetchLocalAds = useCallback(async () => {
    try {
      const res = await fetch("/api/get-local-ads", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          portal: selectedPortal.portal,
        }),
      });

      const result = await res.json();
      if (res.ok && result.exists) {
        setLocalAds(result.ads);
        setLocalAdsExist(true);
      } else {
        setLocalAds([]);
        setLocalAdsExist(false);
      }

      // Return a value to indicate completion
      return { success: res.ok, exists: result.exists };
    } catch (err) {
      console.error("Error fetching local ads:", err);
      setLocalAds([]);
      setLocalAdsExist(false);
      throw err; // Re-throw to allow caller to handle
    }
  }, [selectedPortal.portal]); // Use portal instead of jsonIdentifier as dependency

  // Fetch server ads
  const fetchServerAds = useCallback(async () => {
    if (!authToken) {
      return;
    }

    try {
      const res = await fetch("/api/cms-ads/get", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          token: authToken,
          portal: selectedPortal.portal,
          url: selectedPortal.api,
          environment,
        }),
      });

      const result = await res.json();
      if (res.ok) {
        setServerAds(result.ads);
        setBackendFetched(true);
      }
    } catch (err) {
      console.error("Error fetching server ads:", err);
      setBackendFetched(true); // Still mark as fetched even if there was an error
    }
  }, [authToken, selectedPortal, environment]);

  // Fetch local ads when portal changes
  useEffect(() => {
    fetchLocalAds();
  }, [fetchLocalAds, selectedPortal]);

  // Fetch server ads when auth token changes
  useEffect(() => {
    if (authToken) {
      fetchServerAds();
    }
  }, [fetchServerAds, authToken]);

  return {
    serverAds,
    localAds,
    localAdsExist,
    backendFetched,
    fetchLocalAds,
    fetchServerAds,
    setServerAds,
    setBackendFetched,
  };
}
