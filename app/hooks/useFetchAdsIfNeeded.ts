import { useState, useCallback } from "react";
import { BackendAdvertisement } from "../definitions/adverts.definitions";
import { PortalOption } from "../definitions/app.types";
import { apiClient } from "../utils/api-client";

/**
 * Custom hook to fetch advertisements from the backend if they haven't been fetched yet
 * @param selectedPortal The selected portal
 * @param token Authentication token
 * @param environment Current environment (dev, uat, prod)
 * @param fetchedAds Current fetched ads (if any)
 * @param setFetchedAds Function to update fetched ads
 * @returns A function to fetch ads if needed
 */
export function useFetchAdsIfNeeded(
  selectedPortal: PortalOption,
  token: string,
  environment: string,
  fetchedAds: BackendAdvertisement[] | undefined,
  setFetchedAds: React.Dispatch<React.SetStateAction<BackendAdvertisement[]>>
) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAdsIfNeeded = useCallback(async (forceRefresh: boolean = false): Promise<BackendAdvertisement[]> => {
    // If we already have ads and don't need to force refresh, return them
    if (!forceRefresh && fetchedAds && fetchedAds.length > 0) {
      return fetchedAds;
    }

    setIsLoading(true);
    setError(null);

    try {
      const ads = await apiClient.fetchAds(
        token,
        selectedPortal.portal,
        selectedPortal.api,
        environment
      );

      setFetchedAds(ads);
      return ads;
    } catch (err) {
      const errorMessage = (err as Error).message || "An unknown error occurred";
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [fetchedAds, token, selectedPortal, environment, setFetchedAds]);

  return {
    fetchAdsIfNeeded,
    isLoading,
    error
  };
}
