export const UAT_PORTALS = [
  {
    label: "Mindmegette",
    api: "api-cmstest.content.private",
    portal: "mindmegette",
    jsonIdentifier: "mindmegette",
  },
  {
    label: "<PERSON>gy<PERSON>",
    api: "api-cmstest.content.private",
    portal: "magyar_nemzet",
    jsonIdentifier: "magyarnemzet",
  },
  {
    label: "LIFE",
    api: "api-cmstest.content.private",
    portal: "life",
    jsonIdentifier: "life",
  },
  {
    label: "Koponyeg",
    api: "api-cmstest.content.private",
    portal: "koponyeg",
    jsonIdentifier: "koponyeg",
  },
  {
    label: "Origo",
    api: "api-cmstest.content.private",
    portal: "origo",
    jsonIdentifier: "origo",
  },
  {
    label: "Vilaggazdasag",
    api: "api-cmstest.content.private",
    portal: "vilaggazdasag",
    jsonIdentifier: "vilaggazdasag",
  },

  /// ONLY 1 BORS CAN BE ACTIVE AT A TIME
  {
    label: "Bors",
    api: "api-cmstest.content.private",
    portal: "bors",
    jsonIdentifier: "bors",
  },
  // {
  //   label: "Bors V2",
  //   api: "api-cmstest2.content.private",
  //   portal: "bors",
  //   jsonIdentifier: "bors",
  // },
  {
    label: "Metropol",
    api: "api-cmstest.content.private",
    portal: "metropol",
    jsonIdentifier: "metropol",
  },
  {
    label: "Szegedma",
    api: "api-cmstest.content.private",
    portal: "szegedma",
    jsonIdentifier: "szegedma",
  },
  {
    label: "Riposzt",
    api: "api-cmstest.content.private",
    portal: "ripost",
    jsonIdentifier: "ripost",
  },
  {
    label: "SHE",
    api: "api-cmstest.content.private",
    portal: "she",
    jsonIdentifier: "she",
  },
  {
    label: "Szabadfold",
    api: "api-cmstest.content.private",
    portal: "szabadfold",
    jsonIdentifier: "szabadfold",
  },
  {
    label: "Pesti Sracok",
    api: "api-cmstest.content.private",
    portal: "pesti_sracok",
    jsonIdentifier: "pestisracok",
  },
];
