import {
  EnvironmentOption,
  PortalsByEnvironments,
} from "@/app/definitions/app.types";
import { DEV_PORTALS } from "./dev.consts";
import { UAT_PORTALS } from "./uat.consts";
import { PROD_PORTALS } from "./prod.consts";
import { MEGYEI_LAPOK_PORTALS } from "./megyeiLapok.consts";

export const environments: EnvironmentOption[] = [
  { label: "dev", value: "dev" },
  { label: "uat", value: "uat" },
  { label: "prod", value: "prod" },
  { label: "Megyei Lapok Prod", value: "megyeiLapok" },
];

export const portals: PortalsByEnvironments = {
  dev: DEV_PORTALS,
  uat: UAT_PORTALS,
  prod: PROD_PORTALS,
  megyeiLapok: MEGYEI_LAPOK_PORTALS,
};
