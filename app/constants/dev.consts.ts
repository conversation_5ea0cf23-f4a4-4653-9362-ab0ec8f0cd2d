export const DEV_PORTALS = [
  {
    label: "Mindmegette",
    api: "kozponti-api.dev.trendency.hu",
    portal: "mindmegette",
    jsonIdentifier: "mindmegette",
  },
  {
    label: "<PERSON>gy<PERSON>",
    api: "kozponti-api.dev.trendency.hu",
    portal: "magyar_nemzet",
    jsonIdentifier: "magyarnemzet",
  },
  {
    label: "LIFE",
    api: "kozponti-api.dev.trendency.hu",
    portal: "life",
    jsonIdentifier: "life",
  },
  {
    label: "Koponyeg",
    api: "kozponti-api.dev.trendency.hu",
    portal: "koponyeg",
    jsonIdentifier: "koponyeg",
  },
  {
    label: "Origo",
    api: "kozponti-api.dev.trendency.hu",
    portal: "origo",
    jsonIdentifier: "origo",
  },
  {
    label: "Vilaggazdasag",
    api: "kozponti-api.dev.trendency.hu",
    portal: "vilaggazdasag",
    jsonIdentifier: "vilaggazdasag",
  },
  {
    label: "Bors",
    api: "kozponti-api.dev.trendency.hu",
    portal: "bors",
    jsonIdentifier: "bors",
  },
  {
    label: "Metropol",
    api: "kozponti-api.dev.trendency.hu",
    portal: "metropol",
    jsonIdentifier: "metropol",
  },
  {
    label: "Szegedma",
    api: "kozponti-api.dev.trendency.hu",
    portal: "szegedma",
    jsonIdentifier: "szegedma",
  },
  {
    label: "Riposzt",
    api: "kozponti-api.dev.trendency.hu",
    portal: "ripost",
    jsonIdentifier: "ripost",
  },
  {
    label: "SHE",
    api: "kozponti-api.dev.trendency.hu",
    portal: "she",
    jsonIdentifier: "she",
  },
  {
    label: "Szabadfold",
    api: "kozponti-api.dev.trendency.hu",
    portal: "szabadfold",
    jsonIdentifier: "szabadfold",
  },
  {
    label: "Pesti Sracok",
    api: "kozponti-api.dev.trendency.hu",
    portal: "pesti_sracok",
    jsonIdentifier: "pestisracok",
  },
  {
    label: "FEOL",
    api: "kozponti-api.dev.trendency.hu",
    portal: "feol",
    jsonIdentifier: "feol",
  },
];
