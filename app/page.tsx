"use client";

import LoginForm from "@/app/components/LoginForm";
import { Selector } from "@/app/components/Selector";
import { environments, portals } from "@/app/constants/app.consts";
import { useState } from "react";
import FetchAdsButton from "./components/FetchAdsButton";
import GenerateAdsButton from "./components/GenerateAdsButton";
import ClearAllAdsButton from "./components/ClearAllAdsButton";
import AddAdsButton from "./components/AddAdsButton";
import ActivateAllAdsButton from "./components/ActivateAllAdsButton";
import DoAllAdsButton from "./components/DoAllAdsButton";
import AdvertisementTable from "./components/AdvertisementTable";
import { useAdvertisements } from "./hooks/useAdvertisements";
import SectionCard from "./components/SectionCard";

export default function Home() {
  const [environment, setEnvironment] = useState(environments[0].value);
  const [selectedPortal, setSelectedPortal] = useState(
    portals?.[environment]?.[0]
  );
  const [authToken, setAuthToken] = useState("");

  // Use our custom hook to manage advertisement data
  const {
    serverAds,
    localAds,
    localAdsExist,
    backendFetched,
    fetchLocalAds,
    fetchServerAds,
    setServerAds,
    setBackendFetched,
  } = useAdvertisements(selectedPortal, authToken, environment);

  const onSelectEnvironment = (environment: string) => {
    setEnvironment(environment);
    setAuthToken("");
    setServerAds([]);
    setBackendFetched(false);
    setSelectedPortal(portals?.[environment]?.[0]);
  };

  const onSelectPortal = (portal: string) => {
    const selected = portals[environment].find(
      (portalItem) => portalItem.portal === portal
    );
    setServerAds([]);
    fetchLocalAds();
    setAuthToken("");
    setBackendFetched(false);

    if (selected) setSelectedPortal(selected);
  };

  return (
    <div className="space-y-8 py-6">
      <SectionCard
        number={1}
        title="Portal Selection"
        description="Select the environment and specific portal you want to manage advertisements for."
      >
        <div className="p-4 border rounded-md bg-muted/30">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium mb-2">Environment</h3>
              <p className="text-xs text-muted-foreground mb-3">
                Select the environment (development, staging, production).
              </p>
              <Selector
                onSelect={onSelectEnvironment}
                selectedValue={environment}
                options={environments}
                label={"Environment selector"}
              />
            </div>
            <div>
              <h3 className="text-sm font-medium mb-2">Portal</h3>
              <p className="text-xs text-muted-foreground mb-3">
                Select the specific portal within the chosen environment.
              </p>
              <Selector
                onSelect={onSelectPortal}
                selectedValue={selectedPortal.portal}
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                options={portals?.[environment] as any}
                label={"Portal selector"}
              />
            </div>
          </div>
        </div>
      </SectionCard>

      <SectionCard
        number={2}
        title="Generate Local Advertisements"
        description="Create advertisements from JSON templates in the raw-zones folder."
      >
        <div className="p-4 border rounded-md bg-muted/30">
          <h3 className="text-sm font-medium mb-2">Generate Local JSON</h3>
          <p className="text-xs text-muted-foreground mb-3">
            Generate advertisements from templates for the selected portal. This
            creates local JSON files that can be uploaded to the backend.
          </p>
          <GenerateAdsButton
            portal={selectedPortal.portal}
            jsonIdentifier={selectedPortal.jsonIdentifier}
            onGenerate={fetchLocalAds}
          />
        </div>
      </SectionCard>

      <SectionCard
        number={3}
        title="Login to Portal CMS"
        description="Authenticate with the selected portal to perform backend operations. Required for the API operations."
      >
        <div className="p-4 border rounded-md bg-muted/30">
          <LoginForm
            setAuthToken={setAuthToken}
            authToken={authToken}
            selectedPortal={selectedPortal}
            environment={environment}
          />
        </div>
      </SectionCard>

      <SectionCard
        number={4}
        title="Manage Backend Advertisements"
        description="Use these buttons to manage advertisements in the backend. Chunking with delay between chunks helps avoid rate limit errors."
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 border rounded-md bg-muted/30">
            <h3 className="text-sm font-medium mb-2">
              1. Fetch Advertisements
            </h3>
            <p className="text-xs text-muted-foreground mb-3">
              Retrieve existing advertisements from the backend.
            </p>
            <FetchAdsButton
              setFetchedAds={setServerAds}
              fetchedAds={serverAds}
              token={authToken}
              selectedPortal={selectedPortal}
              environment={environment}
            />
          </div>

          <div className="p-4 border rounded-md bg-muted/30">
            <h3 className="text-sm font-medium mb-2">
              2. Clear Advertisements
            </h3>
            <p className="text-xs text-muted-foreground mb-3">
              Remove all advertisements from the backend.
            </p>
            <ClearAllAdsButton
              setFetchedAds={setServerAds}
              fetchedAds={serverAds}
              token={authToken}
              selectedPortal={selectedPortal}
              environment={environment}
            />
          </div>

          <div className="p-4 border rounded-md bg-muted/30">
            <h3 className="text-sm font-medium mb-2">3. Add Advertisements</h3>
            <p className="text-xs text-muted-foreground mb-3">
              Upload locally generated advertisements to the backend.
            </p>
            <AddAdsButton
              token={authToken}
              selectedPortal={selectedPortal}
              environment={environment}
              onComplete={fetchServerAds}
              fetchedAds={serverAds}
              setFetchedAds={setServerAds}
            />
          </div>

          <div className="p-4 border rounded-md bg-muted/30">
            <h3 className="text-sm font-medium mb-2">
              4. Activate Advertisements
            </h3>
            <p className="text-xs text-muted-foreground mb-3">
              Enable inactive advertisements on the backend.
            </p>
            <ActivateAllAdsButton
              setFetchedAds={setServerAds}
              fetchedAds={serverAds}
              token={authToken}
              selectedPortal={selectedPortal}
              environment={environment}
            />
          </div>
        </div>
      </SectionCard>

      <SectionCard
        number={5}
        title="Automated Workflow"
        description="Run all operations in sequence with a single button. This will fetch, clear, add, and activate advertisements using the configured batching options."
      >
        <div className="p-4 border rounded-md bg-muted/30">
          <h3 className="text-sm font-medium mb-2">Do All Operations</h3>
          <p className="text-xs text-muted-foreground mb-3">
            Execute all operations in sequence: fetch, clear, add, and activate advertisements.
            This uses the chunk size and delay settings you configure below.
          </p>
          <DoAllAdsButton
            setFetchedAds={setServerAds}
            fetchedAds={serverAds}
            token={authToken}
            selectedPortal={selectedPortal}
            environment={environment}
            onComplete={fetchLocalAds}
            setBackendFetched={setBackendFetched}
          />
        </div>
      </SectionCard>

      <hr className="my-8" />

      <SectionCard
        number={6}
        title="Advertisement Status Table"
        description="View and compare local JSON data with backend advertisements."
      >
        {localAdsExist ? (
          <AdvertisementTable
            localAds={localAds}
            serverAds={serverAds}
            backendFetched={backendFetched}
          />
        ) : (
          <div className="p-4 border rounded-md bg-muted/30">
            <h3 className="text-sm font-medium mb-2">No Local Data</h3>
            <p className="text-muted-foreground">
              No local JSON file found for this portal. Please generate ads
              first.
            </p>
          </div>
        )}
      </SectionCard>
    </div>
  );
}
