export type BackendAdvertisement = Readonly<{
  bannerName: AdvertisementBannerName;
  medium: AdvertisementMedium;
  pageType: string;
  zonaId: string;
  masterId: string;
  isAdultAd: boolean;
  isActive: boolean;
  id: string;
}>;

export type AdvertisementMedium = "desktop" | "mobile" | "all"; //Can be used to display the ad on all mediums

export type AdvertisementBannerName =
  | "interstitial"
  | "layer"
  | "leaderboard_1_top"
  | "leaderboard_2_bottom"
  | "medium_rectangle_1_top"
  | "medium_rectangle_2_below_top"
  | "medium_rectangle_3_above_bottom"
  | "medium_rectangle_4_bottom"
  | "roadblock_6_extra_bottom_1"
  | "roadblock_6_extra_bottom_2"
  | "roadblock_1_top"
  | "roadblock_2_below_top"
  | "roadblock_3_above_bottom"
  | "roadblock_4_bottom"
  | "pr_box"
  | "billboard"
  | "footer_ad"
  | "games_page"
  | "medium_rectangle_5_extra_bottom"
  | "medium_rectangle_6_bottom"
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  | any;

export type Advertisement = Readonly<{
  zonaId: string;
  bannerName: AdvertisementBannerName;
  medium: AdvertisementMedium;
  pageType: string;
  priority?: number;
  masterId: string;
  isAdultAd: boolean;
}>;

export type AdvertisementsByMedium = Readonly<{
  desktop: {
    [bannerName in AdvertisementBannerName]: Advertisement;
  };
  mobile: {
    [bannerName in AdvertisementBannerName]: Advertisement;
  };
}>;

export type AdvertisementVariablesByMediums = Readonly<{
  desktop: Advertisement;
  mobile: Advertisement;
}>;
