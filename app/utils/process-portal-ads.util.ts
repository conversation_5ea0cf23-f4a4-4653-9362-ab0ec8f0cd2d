import { BackendAdvertisement } from "../definitions/adverts.definitions";
import allAdverts from "../json/raw-zones/allAds.json";
import {
  RawAdsJson,
  filterAdsByPortal,
  transformAds,
} from "./convert-from-raw-advert.util";
import { convertToCmsAdvert } from "./convert-to-cms-advert.util";
import { writeToJson } from "./write-to-json.util";

interface AdvertConverterParams {
  portal: string;
  jsonIdentifier: string;
  rawAdsData?: RawAdsJson[];
  outputPathPrefix?: string;
}

export function processPortalAds({
  jsonIdentifier,
  portal,
  rawAdsData = allAdverts as RawAdsJson[],
  outputPathPrefix,
}: AdvertConverterParams): BackendAdvertisement[] & { index: number } {
  // Validate raw input data
  if (!rawAdsData || rawAdsData.length === 0) {
    throw new Error("rawAdsData is empty or undefined");
  }

  const portalAds = filterAdsByPortal(rawAdsData, jsonIdentifier);

  // Validate filtered portal ads
  if (!portalAds || portalAds.length === 0) {
    throw new Error(`No ads found for portal: ${jsonIdentifier}`);
  }

  const ads = transformAds(portalAds);

  // Validate transformed ads
  if (!ads || ads.length === 0) {
    throw new Error("Transformed ads array is empty");
  }

  // Process data for different outputs
  const convertedAds = convertToCmsAdvert(ads);
  const visualAds = ads.map((ad, i) => ({
    ...ad,
    index: i,
  })) as never as BackendAdvertisement[] & { index: number };
  const uniqueBanners = ads.reduce(
    (acc: string[], curr: { bannerName: string }): string[] =>
      acc.includes(curr.bannerName) ? acc : [...acc, curr.bannerName],
    []
  );

  // Validate each output before writing
  try {
    if (!uniqueBanners || uniqueBanners.length === 0) {
      throw new Error("uniqueBanners array is empty");
    }
    writeToJson(
      `${outputPathPrefix}/cms-ads-${portal}-only-banners.json`,
      uniqueBanners
    );

    if (!convertedAds || convertedAds.length === 0) {
      throw new Error("convertedAds array is empty");
    }
    writeToJson(`${outputPathPrefix}/cms-ads-${portal}.json`, convertedAds);

    if (!visualAds || visualAds.length === 0) {
      throw new Error("visualAds array is empty");
    }
    writeToJson(`${outputPathPrefix}/cms-ads-${portal}-visual.json`, visualAds);

    return visualAds;
  } catch (error) {
    throw error; // Re-throw to allow calling code to handle
  }
}
