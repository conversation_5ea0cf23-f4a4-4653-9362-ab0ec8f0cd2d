import { BackendAdvertisement } from "../definitions/adverts.definitions";

/**
 * Find a matching server ad with exact criteria
 */
export const findMatchingServerAd = (localAd: BackendAdvertisement, serverAds: BackendAdvertisement[]) => {
  return serverAds.find(
    (serverAd) => {
      // For the cms-ads/get endpoint, we only get id and isActive
      // We need to match based on what we have
      if (serverAd.id && serverAd.id === localAd.id) {
        return true;
      }

      // Match on bannerName, medium, pageType
      // Only check zonaId and masterId if they exist in both local and server
      const basicMatch = serverAd.bannerName === localAd.bannerName &&
        serverAd.medium === localAd.medium &&
        serverAd.pageType === localAd.pageType;

      // For zonaId:
      // - If both have it, they must match
      // - If neither has it, that's fine
      // - If only server has it, that's fine (we don't care about server-only fields)
      // - If only local has it, they don't match (local has data that server doesn't)
      const zonaIdMatch = !localAd.zonaId || (localAd.zonaId && serverAd.zonaId === localAd.zonaId);

      // For masterId:
      // - If both have it, they must match
      // - If neither has it, that's fine
      // - If only server has it, that's fine (we don't care about server-only fields)
      // - If only local has it, they don't match (local has data that server doesn't)
      const masterIdMatch = !localAd.masterId || (localAd.masterId && serverAd.masterId === localAd.masterId);

      return basicMatch && zonaIdMatch && masterIdMatch;
    }
  );
};

/**
 * Check if an ad exists in the backend with matching name, pagetype, and medium
 */
export const existsInBackendByBasicCriteria = (localAd: BackendAdvertisement, serverAds: BackendAdvertisement[]) => {
  return serverAds.some(
    (serverAd) =>
      serverAd.bannerName === localAd.bannerName &&
      serverAd.pageType === localAd.pageType &&
      serverAd.medium === localAd.medium
  );
};

/**
 * Find a server ad with matching basic criteria (name, pagetype, medium)
 */
export const findServerAdByBasicCriteria = (localAd: BackendAdvertisement, serverAds: BackendAdvertisement[]) => {
  return serverAds.find(
    (serverAd) =>
      serverAd.bannerName === localAd.bannerName &&
      serverAd.pageType === localAd.pageType &&
      serverAd.medium === localAd.medium
  );
};

/**
 * Check if values differ
 */
export const valuesDiffer = (localValue: string, serverValue: string | undefined): boolean => {
  if (!serverValue) return false;
  return localValue !== serverValue;
};

/**
 * Check if the row has differences
 */
export const rowHasDifferences = (localAd: BackendAdvertisement, serverAd: BackendAdvertisement | undefined): boolean => {
  if (!serverAd) return false;
  return valuesDiffer(localAd.masterId, serverAd.masterId) ||
         valuesDiffer(localAd.zonaId, serverAd.zonaId);
};
