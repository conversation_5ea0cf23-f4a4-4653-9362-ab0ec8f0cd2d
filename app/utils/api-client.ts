import { BackendAdvertisement } from "../definitions/adverts.definitions";
import { ConvertedAdItem } from "./convert-to-cms-advert.util";

/**
 * Centralized API client for all advertisement operations
 */
export const apiClient = {
  /**
   * Fetch advertisements from the backend
   */
  fetchAds: async (
    token: string,
    portal: string,
    api: string,
    environment: string
  ): Promise<BackendAdvertisement[]> => {
    const res = await fetch("/api/cms-ads/get", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        token,
        portal,
        url: api,
        environment,
      }),
    });

    if (!res.ok) {
      const error = await res.json();
      throw new Error(error.error || `Failed with status ${res.status}`);
    }

    const result = await res.json();
    return result.ads;
  },

  /**
   * Add an advertisement to the backend
   */
  addAd: async (
    token: string,
    portal: string,
    api: string,
    environment: string,
    advert: ConvertedAdItem
  ): Promise<unknown> => {
    const response = await fetch("/api/cms-ads/add", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        token,
        portal,
        url: api,
        advert: advert.item,
        environment,
      }),
    });

    const result = await response.json();

    if (!response.ok || !result.success) {
      throw new Error(result.error || `Failed with status ${response.status}`);
    }

    return result;
  },

  /**
   * Delete an advertisement from the backend
   */
  deleteAd: async (
    token: string,
    portal: string,
    api: string,
    environment: string,
    adId: string
  ): Promise<unknown> => {
    const response = await fetch("/api/cms-ads/delete", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        token,
        portal,
        url: api,
        adId,
        environment,
      }),
    });

    const result = await response.json();

    if (!response.ok || !result.success) {
      throw new Error(result.error || `Failed with status ${response.status}`);
    }

    return result;
  },

  /**
   * Activate an advertisement in the backend
   */
  activateAd: async (
    token: string,
    portal: string,
    api: string,
    environment: string,
    adId: string
  ): Promise<unknown> => {
    const response = await fetch("/api/cms-ads/activate", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        token,
        portal,
        url: api,
        adId,
        environment,
      }),
    });

    const result = await response.json();

    if (!response.ok || !result.success) {
      throw new Error(result.error || `Failed with status ${response.status}`);
    }

    return result;
  },

  /**
   * Fetch local advertisements
   */
  fetchLocalAds: async (portal: string): Promise<{
    success: boolean;
    exists: boolean;
    ads?: BackendAdvertisement[];
  }> => {
    const res = await fetch("/api/get-local-ads", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        portal,
      }),
    });

    const result = await res.json();
    return {
      success: res.ok,
      exists: result.exists,
      ads: result.ads,
    };
  },

  /**
   * Generate advertisements from JSON
   */
  generateAds: async (
    portal: string,
    jsonIdentifier: string,
    jsonFile: string
  ): Promise<BackendAdvertisement[]> => {
    const res = await fetch("/api/generate-ads", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        portal,
        jsonIdentifier,
        jsonFile,
      }),
    });

    if (!res.ok) {
      const error = await res.json();
      throw new Error(error.error || `Failed with status ${res.status}`);
    }

    const result = await res.json();
    return result.adverts;
  },

  /**
   * Get available JSON files
   */
  getJsonFiles: async (): Promise<{ name: string; path: string }[]> => {
    const res = await fetch("/api/get-raw-json-files");

    if (!res.ok) {
      throw new Error(`Failed to fetch JSON files: ${res.status}`);
    }

    const result = await res.json();
    return result.files;
  },

  /**
   * Login to get authentication token
   */
  login: async (
    email: string,
    password: string,
    portal: { portal: string; api: string },
    environment: string
  ): Promise<string> => {
    const response = await fetch("/api/login", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        email,
        password,
        portal,
        environment,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || `Login failed with status ${response.status}`);
    }

    const result = await response.json();
    return result.token;
  }
};
