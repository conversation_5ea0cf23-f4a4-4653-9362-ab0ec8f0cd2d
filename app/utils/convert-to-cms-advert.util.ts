interface AssertLength {
  charset: string;
  charsetMessage: string;
  exactMessage: string | null;
  max: number | null;
  maxMessage: string;
  min: number | null;
  minMessage: string | null;
}

interface AssertNotBlank {
  allowNull: boolean;
  message: string;
}

type Assertions =
  | {
      Length?: AssertLength;
      NotBlank?: AssertNotBlank;
    }
  | []; // Allow empty array for checkbox fields

interface ReferenceSource {
  url: string;
  selectKeyProperty: string;
  selectDisplayProperty: string;
  optionalParameters: never[];
  requiredParameters: never[];
}

interface InputInfo {
  simpleSelect?: boolean;
  [key: string]: unknown;
}

interface InputField {
  inputType: "text" | "checkbox" | "select";
  key: string;
  value: unknown;
  multiple: boolean;
  inputLabel: string;
  readOnly: boolean;
  asserts: Assertions;
  dependency: null;
  inputInfo: InputInfo | [];
  referenceSource?: ReferenceSource;
  referenceUpload?: null;
}

interface ItemInfo {
  entityAsserts: never[];
}

interface ItemMeta {
  responseType: string;
  dataCount: number;
  requestUrl: string;
  id: string | null;
}

export interface ConvertedAdItem {
  item: {
    data: InputField[];
    info: ItemInfo;
    meta: ItemMeta;
  };
}

interface RawAd {
  zonaId: string;
  isAdultAd: boolean;
  masterId: string;
  medium: string;
  pageType: string;
  bannerName: string;
}

export function convertToCmsAdvert(ads: RawAd[]): ConvertedAdItem[] {
  return ads.map((ad) => ({
    item: {
      data: [
        {
          inputType: "text" as const,
          key: "zonaId",
          value: ad.zonaId,
          multiple: false,
          inputLabel: "zonaId",
          readOnly: false,
          asserts: {
            Length: {
              charset: "UTF-8",
              charsetMessage:
                "Ez az érték nem az elvárt UTF-8 karakterkódolást használja.",
              exactMessage: null,
              max: 255,
              maxMessage:
                "Ez az érték túl hosszú. Legfeljebb 255 karaktert tartalmazhat.|Ez az érték túl hosszú. Legfeljebb 255 karaktert tartalmazhat.",
              min: 2,
              minMessage:
                "Ez az érték túl rövid. Legalább 2 karaktert kell tartalmaznia.|Ez az érték túl rövid. Legalább 2 karaktert kell tartalmaznia.",
            },
          },
          dependency: null,
          inputInfo: [],
        },
        {
          inputType: "checkbox" as const,
          key: "isAdultAd",
          value: ad.isAdultAd,
          multiple: false,
          inputLabel: "isAdultAd",
          readOnly: false,
          asserts: [],
          dependency: null,
          inputInfo: [],
        },
        {
          inputType: "text" as const,
          key: "masterId",
          value: ad.masterId,
          multiple: false,
          inputLabel: "masterId",
          readOnly: false,
          asserts: [],
          dependency: null,
          inputInfo: [],
        },
        {
          inputType: "select" as const,
          key: "medium",
          value: ad.medium,
          multiple: false,
          inputLabel: "medium",
          readOnly: false,
          asserts: {
            NotBlank: {
              allowNull: false,
              message: "Ez az érték nem lehet üres.",
            },
            Length: {
              charset: "UTF-8",
              charsetMessage:
                "Ez az érték nem az elvárt UTF-8 karakterkódolást használja.",
              exactMessage: null,
              max: 31,
              maxMessage:
                "Ez az érték túl hosszú. Legfeljebb 31 karaktert tartalmazhat.|Ez az érték túl hosszú. Legfeljebb 31 karaktert tartalmazhat.",
              min: null,
              minMessage: null,
            },
          },
          dependency: null,
          inputInfo: {
            simpleSelect: true,
          },
          referenceSource: {
            url: "http://kozponti-api.dev.trendency.hu/api/hu/hu/source/portal/commercial/medium",
            selectKeyProperty: "value",
            selectDisplayProperty: "label",
            optionalParameters: [],
            requiredParameters: [],
          },
          referenceUpload: null,
        },
        {
          inputType: "select" as const,
          key: "pageType",
          value: ad.pageType,
          multiple: false,
          inputLabel: "pageType",
          readOnly: false,
          asserts: {
            Length: {
              charset: "UTF-8",
              charsetMessage:
                "Ez az érték nem az elvárt UTF-8 karakterkódolást használja.",
              exactMessage: null,
              max: 255,
              maxMessage:
                "Ez az érték túl hosszú. Legfeljebb 255 karaktert tartalmazhat.|Ez az érték túl hosszú. Legfeljebb 255 karaktert tartalmazhat.",
              min: 2,
              minMessage:
                "Ez az érték túl rövid. Legalább 2 karaktert kell tartalmaznia.|Ez az érték túl rövid. Legalább 2 karaktert kell tartalmaznia.",
            },
          },
          dependency: null,
          inputInfo: {
            simpleSelect: true,
          },
          referenceSource: {
            url: "http://kozponti-api.dev.trendency.hu/api/hu/hu/source/portal/commercial/page_type",
            selectKeyProperty: "value",
            selectDisplayProperty: "label",
            optionalParameters: [],
            requiredParameters: [],
          },
          referenceUpload: null,
        },
        {
          inputType: "select" as const,
          key: "bannerName",
          value: ad.bannerName,
          multiple: false,
          inputLabel: "bannerName",
          readOnly: false,
          asserts: {
            Length: {
              charset: "UTF-8",
              charsetMessage:
                "Ez az érték nem az elvárt UTF-8 karakterkódolást használja.",
              exactMessage: null,
              max: 255,
              maxMessage:
                "Ez az érték túl hosszú. Legfeljebb 255 karaktert tartalmazhat.|Ez az érték túl hosszú. Legfeljebb 255 karaktert tartalmazhat.",
              min: 2,
              minMessage:
                "Ez az érték túl rövid. Legalább 2 karaktert kell tartalmaznia.|Ez az érték túl rövid. Legalább 2 karaktert kell tartalmaznia.",
            },
          },
          dependency: null,
          inputInfo: {
            simpleSelect: true,
          },
          referenceSource: {
            url: "http://kozponti-api.dev.trendency.hu/api/hu/hu/source/portal/commercial/banner_name",
            selectKeyProperty: "value",
            selectDisplayProperty: "label",
            optionalParameters: [],
            requiredParameters: [],
          },
          referenceUpload: null,
        },
      ],
      info: {
        entityAsserts: [],
      },
      meta: {
        responseType: "form",
        dataCount: 6,
        requestUrl: "/api/hu/hu/portal/commercial",
        id: null,
      },
    },
  }));
}
