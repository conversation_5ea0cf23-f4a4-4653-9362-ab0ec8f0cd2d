/**
 * Utility functions for advertisement operations
 */
import { BackendAdvertisement } from "../definitions/adverts.definitions";
import { ConvertedAdItem } from "./convert-to-cms-advert.util";

/**
 * Creates a delay of the specified milliseconds
 */
export const delayMs = (ms: number): Promise<void> =>
  new Promise((resolve) => setTimeout(resolve, ms));

/**
 * Splits an array into chunks of the specified size
 */
export function createChunks<T>(items: T[], chunkSize: number): T[][] {
  const chunks: T[][] = [];
  for (let i = 0; i < items.length; i += chunkSize) {
    chunks.push(items.slice(i, i + chunkSize));
  }
  return chunks;
}

/**
 * Processes chunks with a delay between each chunk
 */
export async function processChunksWithDelay<T>(
  chunks: T[][],
  processChunk: (chunk: T[]) => Promise<void>,
  delay: number,
  onChunkStart?: (
    chunkIndex: number,
    totalChunks: number,
    chunkSize: number
  ) => void,
  onDelayStart?: (delayMs: number) => void
): Promise<void> {
  for (let i = 0; i < chunks.length; i++) {
    const chunk = chunks[i];

    if (onChunkStart) {
      onChunkStart(i + 1, chunks.length, chunk.length);
    }

    await processChunk(chunk);

    if (i < chunks.length - 1 && delay > 0) {
      if (onDelayStart) {
        onDelayStart(delay);
      }
      await delayMs(delay);
    }
  }
}

/**
 * Processes backend advertisements in chunks
 */
export async function processAdsInChunks(
  ads: BackendAdvertisement[],
  chunkSize: number,
  delay: number,
  operation: "delete" | "activate",
  token: string,
  portal: string,
  api: string,
  environment?: string,
  onChunkStart?: (
    chunkIndex: number,
    totalChunks: number,
    chunkSize: number
  ) => void,
  onDelayStart?: (delayMs: number) => void,
  onOperationResult?: (results: {
    total: number,
    success: number,
    failure: number,
    failedAds: Array<{ad: BackendAdvertisement, error: string}>
  }) => void
): Promise<{
  total: number,
  success: number,
  failure: number,
  failedAds: Array<{ad: BackendAdvertisement, error: string}>
}> {
  const chunks = createChunks(ads, chunkSize);

  // Track successful and failed operations
  let successCount = 0;
  let failureCount = 0;
  const failedAds: Array<{ad: BackendAdvertisement, error: string}> = [];

  await processChunksWithDelay(
    chunks,
    async (chunk) => {
      const endpoint =
        operation === "delete"
          ? "/api/cms-ads/delete"
          : "/api/cms-ads/activate";

      const results = await Promise.all(
        chunk.map(async (ad) => {
          try {
            const response = await fetch(endpoint, {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                token,
                portal,
                url: api,
                adId: ad.id,
                environment,
              }),
            });

            const result = await response.json();

            if (!response.ok || !result.success) {
              const errorMsg = result.error || `Failed with status ${response.status}`;

              failureCount++;
              return { success: false, ad, error: errorMsg };
            }

            successCount++;
            return { success: true, ad, result: result.result };
          } catch (error) {
            const errorMsg = (error as Error)?.message || "Unknown error";

            failureCount++;
            return { success: false, ad, error: errorMsg };
          }
        })
      );

      // Collect failed ads for reporting
      results.forEach(result => {
        if (!result.success) {
          failedAds.push({ ad: result.ad, error: result.error });
        }
      });
    },
    delay,
    onChunkStart,
    onDelayStart
  );

  // Check for failures
  if (failureCount > 0) {
    // Prepare failure information for reporting
    const failureInfo = failedAds.slice(0, 5).map(f => ({
      id: f.ad.id,
      bannerName: f.ad.bannerName,
      error: f.error
    }));

    // This information can be used for detailed logging if needed
    void failureInfo;
  }

  const results = {
    total: ads.length,
    success: successCount,
    failure: failureCount,
    failedAds: failedAds.slice(0, 10) // Return up to 10 failed ads for reporting
  };

  // Call the result callback if provided
  if (onOperationResult) {
    onOperationResult(results);
  }

  return results;
}

/**
 * Processes local advertisements in chunks to add them to the backend
 * Only adds advertisements that don't exist in the backend
 */
export async function addAdsInChunks(
  localAds: ConvertedAdItem[],
  chunkSize: number,
  delay: number,
  token: string,
  portal: string,
  api: string,
  environment: string,
  serverAds: BackendAdvertisement[] = [],
  onChunkStart?: (
    chunkIndex: number,
    totalChunks: number,
    chunkSize: number
  ) => void,
  onDelayStart?: (delayMs: number) => void,
  onFilterResults?: (total: number, filtered: number) => void
): Promise<{
  total: number,
  added: number,
  success: number,
  failure: number,
  failedAds: Array<{ad: ConvertedAdItem, error: string}>
}> {
  // Verify the format of the local ads data
  if (localAds.length > 0) {
    const sampleAd = localAds[0];


    if (!sampleAd.item || !Array.isArray(sampleAd.item.data)) {

      throw new Error("Invalid local ad format");
    }
  }

  // Extract the basic ad information from the ConvertedAdItem
  const extractAdInfo = (ad: ConvertedAdItem): { bannerName: string, medium: string, pageType: string, zonaId?: string, masterId?: string } => {
    const data = ad.item.data;
    const bannerName = data.find(field => field.key === "bannerName")?.value as string;
    const medium = data.find(field => field.key === "medium")?.value as string;
    const pageType = data.find(field => field.key === "pageType")?.value as string;
    const zonaId = data.find(field => field.key === "zonaId")?.value as string;
    const masterId = data.find(field => field.key === "masterId")?.value as string;
    return { bannerName, medium, pageType, zonaId, masterId };
  };



  // Verify the format of the backend ads data
  if (serverAds.length > 0) {
    const sampleAd = serverAds[0];
    // Check if the backend ad has all required fields
    if (!sampleAd.bannerName || !sampleAd.medium || !sampleAd.pageType) {
      // Backend ad is missing required fields
    }
  }

  // Create a map of backend ads for faster lookup
  const backendAdsMap = new Map<string, BackendAdvertisement>();
  serverAds.forEach(ad => {
    if (ad.bannerName && ad.medium && ad.pageType) {
      const key = `${ad.bannerName}|${ad.medium}|${ad.pageType}`;
      backendAdsMap.set(key, ad);
    }
    // Skip backend ads with missing fields
  });

  // Filter out ads that already exist in the backend
  const filteredAds = localAds.filter(localAd => {
    const { bannerName, medium, pageType } = extractAdInfo(localAd);

    // Skip if any required field is missing
    if (!bannerName || !medium || !pageType) {
      // Skip ad with missing required fields
      return false;
    }

    // Create a lookup key
    const key = `${bannerName}|${medium}|${pageType}`;

    // Check if this ad exists in the backend
    const exists = backendAdsMap.has(key);

    // If it doesn't exist, we should add it
    return !exists;
  });

  // Filter ads that don't exist in the backend

  // Report filtering results if callback provided
  if (onFilterResults) {
    onFilterResults(localAds.length, filteredAds.length);
  }

  // If no ads to add, return early
  if (filteredAds.length === 0) {
    // No ads to add - all local ads already exist in the backend
    return {
      total: localAds.length,
      added: 0,
      success: 0,
      failure: 0,
      failedAds: []
    };
  }

  const chunks = createChunks(filteredAds, chunkSize);

  // Track successful and failed adds
  let successCount = 0;
  let failureCount = 0;
  const failedAds: Array<{ad: ConvertedAdItem, error: string}> = [];

  await processChunksWithDelay(
    chunks,
    async (chunk) => {
      const results = await Promise.all(
        chunk.map(async (ad) => {
          try {
            const response = await fetch("/api/cms-ads/add", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                token,
                portal,
                url: api,
                advert: ad.item,
                environment,
              }),
            });

            const result = await response.json();

            if (!response.ok || !result.success) {
              const errorMsg = result.error || `Failed with status ${response.status}`;

              failureCount++;
              return { success: false, ad, error: errorMsg };
            }

            successCount++;
            return { success: true, ad, result: result.result };
          } catch (error) {
            const errorMsg = (error as Error)?.message || "Unknown error";

            failureCount++;
            return { success: false, ad, error: errorMsg };
          }
        })
      );

      // Collect failed ads for reporting
      results.forEach(result => {
        if (!result.success) {
          failedAds.push({ ad: result.ad, error: result.error });
        }
      });
    },
    delay,
    onChunkStart,
    onDelayStart
  );

  // Check for failures
  if (failureCount > 0) {
    // Prepare failure information for reporting
    const failureInfo = failedAds.slice(0, 5).map(f => ({
      info: extractAdInfo(f.ad),
      error: f.error
    }));

    // This information can be used for detailed logging if needed
    void failureInfo;
  }

  return {
    total: localAds.length,
    added: filteredAds.length,
    success: successCount,
    failure: failureCount,
    failedAds: failedAds.slice(0, 10) // Return up to 10 failed ads for reporting
  };
}

/**
 * Performs multiple fetch attempts with delays to ensure backend state is up-to-date
 */
export async function fetchWithMultipleAttempts(
  fetchFunction: (forceRefresh?: boolean) => Promise<BackendAdvertisement[]>,
  attempts: number = 3,
  delayBetweenAttempts: number = 2000,
  onAttemptStart?: (attempt: number, totalAttempts: number) => void,
  onDelayStart?: (delayMs: number) => void
): Promise<BackendAdvertisement[]> {
  let result: BackendAdvertisement[] = [];

  for (let i = 0; i < attempts; i++) {
    if (onAttemptStart) {
      onAttemptStart(i + 1, attempts);
    }

    // Always force a refresh to get the latest data
    result = await fetchFunction(true);

    if (i < attempts - 1) {
      if (onDelayStart) {
        onDelayStart(delayBetweenAttempts);
      }
      await delayMs(delayBetweenAttempts);
    }
  }

  return result;
}
