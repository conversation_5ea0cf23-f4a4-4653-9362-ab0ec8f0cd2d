import { BackendAdvertisement } from "../definitions/adverts.definitions";
import { ConvertedAdItem } from "./convert-to-cms-advert.util";
import { addAdsInChunks, delayMs, processAdsInChunks } from "./ad-operations.util";

export type StatusCallback = (message: string) => void;
export type FetchAdsFunction = (forceRefresh?: boolean) => Promise<BackendAdvertisement[]>;

export interface OperationSequenceOptions {
  portal: string;
  api: string;
  token: string;
  environment: string;
  chunkSize: number;
  delay: number;
  addStatusMessage: StatusCallback;
  fetchAdsIfNeeded: FetchAdsFunction;
}

/**
 * Standard delay between operations
 */
export const OPERATION_DELAY = 5000; // 7 seconds between operations

/**
 * Step 1: Fetch advertisements from backend
 */
export async function fetchAdsStep({
  addStatusMessage,
  fetchAdsIfNeeded
}: Pick<OperationSequenceOptions, 'addStatusMessage' | 'fetchAdsIfNeeded'>): Promise<BackendAdvertisement[]> {
  addStatusMessage("Fetching advertisements from backend...");

  try {
    // Force refresh to ensure we get the latest data
    const initialAds = await fetchAdsIfNeeded(true);
    addStatusMessage(`✅ Successfully fetched ${initialAds.length} advertisements`);

    // Standard delay between operations
    addStatusMessage(`Waiting ${OPERATION_DELAY/1000} seconds before next operation...`);
    await delayMs(OPERATION_DELAY);

    return initialAds;
  } catch (err) {
    addStatusMessage(`❌ Error fetching ads: ${(err as Error).message}`);
    throw err;
  }
}

/**
 * Step 2: Clear existing advertisements
 */
export async function clearAdsStep({
  portal,
  api,
  token,
  environment,
  chunkSize,
  delay,
  addStatusMessage,
  fetchAdsIfNeeded
}: OperationSequenceOptions): Promise<void> {
  addStatusMessage("Clearing existing advertisements...");

  try {
    // Always fetch the latest backend state before clearing
    addStatusMessage("Fetching latest backend state before clearing...");
    const ads = await fetchAdsIfNeeded(true);
    addStatusMessage(`Found ${ads.length} advertisements in backend`);

    if (ads.length === 0) {
      addStatusMessage("No advertisements to clear");
    } else {
      await processAdsInChunks(
        ads,
        chunkSize,
        delay,
        "delete",
        token,
        portal,
        api,
        environment,
        (chunkIndex, totalChunks, chunkSize) => {
          addStatusMessage(`Clearing chunk ${chunkIndex} of ${totalChunks} (${chunkSize} ads)`);
        },
        (delayTime) => {
          addStatusMessage(`Waiting ${delayTime/1000} seconds before next chunk...`);
        }
      );

      addStatusMessage(`✅ Successfully cleared ${ads.length} advertisements`);
    }

    // Standard delay after clearing
    addStatusMessage("Waiting for backend to process clear operation...");
    await delayMs(OPERATION_DELAY);
  } catch (err) {
    addStatusMessage(`❌ Error clearing ads: ${(err as Error).message}`);
    throw err;
  }
}

/**
 * Step 3: Fetch advertisements after clearing
 */
export async function fetchAfterClearStep({
  addStatusMessage,
  fetchAdsIfNeeded
}: Pick<OperationSequenceOptions, 'addStatusMessage' | 'fetchAdsIfNeeded'>): Promise<BackendAdvertisement[]> {
  addStatusMessage("Fetching advertisements after clearing...");

  try {
    const afterClearAds = await fetchAdsIfNeeded(true);
    addStatusMessage(`✅ Backend state after clearing: ${afterClearAds.length} advertisements`);

    // Standard delay after fetch
    addStatusMessage(`Waiting ${OPERATION_DELAY/1000} seconds before next operation...`);
    await delayMs(OPERATION_DELAY);

    return afterClearAds;
  } catch (err) {
    addStatusMessage(`❌ Error fetching ads after clearing: ${(err as Error).message}`);
    throw err;
  }
}

/**
 * Step 4: Add advertisements
 * Returns the number of ads that were added
 */
export async function addAdsStep({
  portal,
  api,
  token,
  environment,
  chunkSize,
  delay,
  addStatusMessage,
  fetchAdsIfNeeded
}: OperationSequenceOptions): Promise<number> {
  try {
    // Always fetch the latest backend state before adding
    addStatusMessage("Fetching latest backend state before adding...");
    const currentBackendAds = await fetchAdsIfNeeded(true);
    addStatusMessage(`Current backend state: ${currentBackendAds.length} advertisements`);

    addStatusMessage("Reading local advertisements...");
    const res = await fetch("/api/get-json", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ portal }),
    });

    if (!res.ok) {
      throw new Error("Failed to get local JSON data");
    }

    const data = await res.json();

    // Check if the data is in the expected format
    if (!Array.isArray(data)) {
      throw new Error("Invalid data format from /api/get-json. Expected an array.");
    }

    const localAds: ConvertedAdItem[] = data;
    addStatusMessage(`Found ${localAds.length} local advertisements`);

    // Store the number of ads to add
    let adsToAddCount = 0;

    // Process ads in chunks, only adding those that don't exist in the backend
    const result = await addAdsInChunks(
      localAds,
      chunkSize,
      delay,
      token,
      portal,
      api,
      environment,
      currentBackendAds,
      (chunkIndex, totalChunks, chunkSize) => {
        addStatusMessage(`Adding chunk ${chunkIndex} of ${totalChunks} (${chunkSize} ads)`);
      },
      (delayTime) => {
        addStatusMessage(`Waiting ${delayTime/1000} seconds before next chunk...`);
      },
      (total, filtered) => {
        addStatusMessage(`Found ${total} local ads, ${filtered} are missing from backend and will be added.`);

        // Store the number of ads added for later comparison
        adsToAddCount = filtered;
      }
    );

    if (result.added === 0) {
      addStatusMessage(`✅ All ${result.total} advertisements already exist in the backend. Nothing to add.`);
    } else {
      // Check if there were any failures
      if (result.failure > 0) {
        addStatusMessage(`⚠️ Added ${result.success} out of ${result.added} advertisements with ${result.failure} failures. ${result.total - result.added} already existed in the backend.`);

        // Log sample of failed adds
        const sampleFailures = result.failedAds.slice(0, 3);
        sampleFailures.forEach(failure => {
          const info = failure.ad.item.data.find(field => field.key === "bannerName")?.value || "unknown";
          addStatusMessage(`  - Failed to add ad "${info}": ${failure.error}`);
        });
      } else {
        addStatusMessage(`✅ Successfully added ${result.added} out of ${result.total} advertisements. ${result.total - result.added} already existed in the backend.`);
      }
    }

    // Standard delay after adding
    addStatusMessage("Waiting for backend to process add operation...");
    await delayMs(OPERATION_DELAY);

    return adsToAddCount;
  } catch (err) {
    addStatusMessage(`❌ Error adding ads: ${(err as Error).message}`);
    throw err;
  }
}

/**
 * Step 5: Fetch advertisements after adding
 */
export async function fetchAfterAddStep({
  addStatusMessage,
  fetchAdsIfNeeded,
  adsToAddCount
}: Pick<OperationSequenceOptions, 'addStatusMessage' | 'fetchAdsIfNeeded'> & { adsToAddCount: number }): Promise<BackendAdvertisement[]> {
  addStatusMessage("Fetching advertisements after adding...");
  let afterAddAds: BackendAdvertisement[] = [];

  try {
    // Try multiple fetches with longer delays to ensure we get the latest state
    addStatusMessage("Performing multiple fetches to ensure we get the latest backend state...");

    // First fetch
    afterAddAds = await fetchAdsIfNeeded(true);
    let activeCount = afterAddAds.filter(ad => ad?.isActive).length;
    let inactiveCount = afterAddAds.filter(ad => !ad?.isActive).length;
    addStatusMessage(`Backend state (first fetch): ${afterAddAds.length} total ads (${activeCount} active, ${inactiveCount} inactive)`);

    // Wait longer - use our standard operation delay
    addStatusMessage(`Waiting ${OPERATION_DELAY/1000} seconds before second fetch...`);
    await delayMs(OPERATION_DELAY);

    // Second fetch
    afterAddAds = await fetchAdsIfNeeded(true);
    activeCount = afterAddAds.filter(ad => ad?.isActive).length;
    inactiveCount = afterAddAds.filter(ad => !ad?.isActive).length;
    addStatusMessage(`✅ Backend state after adding: ${afterAddAds.length} total ads (${activeCount} active, ${inactiveCount} inactive)`);

    // Check if the number of ads matches what we expect
    if (afterAddAds.length < adsToAddCount) {
      addStatusMessage(`⚠️ Warning: Expected at least ${adsToAddCount} total ads, but found ${afterAddAds.length}.`);
      addStatusMessage(`This may indicate that some ads were not successfully added or the backend has a limit on the number of ads.`);

      // Add diagnostic information
      addStatusMessage(`⚠️ Note: Using portal for all operations.`);

      // Add more diagnostic information
      const pageTypes = new Set(afterAddAds.map(ad => ad.pageType));
      addStatusMessage(`Page types in backend: ${Array.from(pageTypes).join(', ')}`);

      // Sample a few ads for debugging
      const sampleSize = Math.min(3, afterAddAds.length);
      if (sampleSize > 0) {
        addStatusMessage(`Sample of backend ads (${sampleSize}):`);
        for (let i = 0; i < sampleSize; i++) {
          const ad = afterAddAds[i];
          addStatusMessage(`  - ID: ${ad.id}, Name: ${ad.bannerName}, Active: ${ad.isActive}, PageType: ${ad.pageType}`);
        }
      }
    }

    // Standard delay after fetch
    addStatusMessage(`Waiting ${OPERATION_DELAY/1000} seconds before next operation...`);
    await delayMs(OPERATION_DELAY);

    return afterAddAds;
  } catch (err) {
    addStatusMessage(`❌ Error fetching ads after adding: ${(err as Error).message}`);
    throw err;
  }
}

/**
 * Step 6: Activate advertisements
 */
export async function activateAdsStep({
  portal,
  api,
  token,
  environment,
  chunkSize,
  delay,
  addStatusMessage,
  fetchAdsIfNeeded
}: OperationSequenceOptions): Promise<BackendAdvertisement[]> {
  addStatusMessage("Activating advertisements...");

  try {
    // Always fetch the latest backend state before activation
    addStatusMessage("Fetching latest backend state before activation...");
    const latestAds = await fetchAdsIfNeeded(true);

    // Filter for inactive ads
    const inactiveAds = latestAds.filter((ad) => !ad?.isActive);
    const inactiveCount = inactiveAds.length;

    addStatusMessage(`Found ${inactiveCount} inactive advertisements to activate`);

    if (inactiveAds.length === 0) {
      addStatusMessage("No inactive advertisements found to activate");
    } else {
      const activationResults = await processAdsInChunks(
        inactiveAds,
        chunkSize,
        delay,
        "activate",
        token,
        portal,
        api,
        environment,
        (chunkIndex, totalChunks, chunkSize) => {
          addStatusMessage(`Activating chunk ${chunkIndex} of ${totalChunks} (${chunkSize} ads)`);
        },
        (delayTime) => {
          addStatusMessage(`Waiting ${delayTime/1000} seconds before next chunk...`);
        },
        (results) => {
          // Report detailed results
          if (results.failure > 0) {
            addStatusMessage(`⚠️ Warning: ${results.failure} out of ${results.total} activation requests failed`);

            // Log sample of failed activations
            const sampleFailures = results.failedAds.slice(0, 3);
            sampleFailures.forEach(failure => {
              addStatusMessage(`  - Failed to activate ad ${failure.ad.id}: ${failure.error}`);
            });
          }
        }
      );

      if (activationResults.failure > 0) {
        addStatusMessage(`⚠️ Completed with ${activationResults.success} successful and ${activationResults.failure} failed activations`);
      } else {
        addStatusMessage(`✅ Successfully activated all ${inactiveAds.length} advertisements`);
      }

      // Add a much longer delay after activation
      addStatusMessage("Waiting for backend to process activation (10 seconds)...");
      await delayMs(10000); // Increased from 5 to 10 seconds
    }

    // Force multiple refreshes with progressively longer delays to ensure we get the updated state
    addStatusMessage("Refreshing backend state after activation (attempt 1)...");
    let refreshedAds = await fetchAdsIfNeeded(true);
    const attempt1ActiveCount = refreshedAds.filter(ad => ad?.isActive).length;
    const attempt1InactiveCount = refreshedAds.filter(ad => !ad?.isActive).length;
    addStatusMessage(`Backend state (attempt 1): ${refreshedAds.length} total ads (${attempt1ActiveCount} active, ${attempt1InactiveCount} inactive)`);

    // Wait longer for second attempt - use our standard operation delay
    addStatusMessage(`Waiting ${OPERATION_DELAY/1000} seconds for backend to update...`);
    await delayMs(OPERATION_DELAY);

    addStatusMessage("Refreshing backend state after activation (attempt 2)...");
    refreshedAds = await fetchAdsIfNeeded(true);
    const attempt2ActiveCount = refreshedAds.filter(ad => ad?.isActive).length;
    const attempt2InactiveCount = refreshedAds.filter(ad => !ad?.isActive).length;
    addStatusMessage(`Backend state (attempt 2): ${refreshedAds.length} total ads (${attempt2ActiveCount} active, ${attempt2InactiveCount} inactive)`);

    // Wait even longer for final attempt - use a longer delay for final check
    addStatusMessage("Waiting 10 seconds for final backend update...");
    await delayMs(10000);

    addStatusMessage("Refreshing backend state after activation (final attempt)...");
    refreshedAds = await fetchAdsIfNeeded(true);

    // Log the final state
    const finalActiveCount = refreshedAds.filter(ad => ad?.isActive).length;
    const finalInactiveCount = refreshedAds.filter(ad => !ad?.isActive).length;
    addStatusMessage(`Final backend status: ${refreshedAds.length} total ads (${finalActiveCount} active, ${finalInactiveCount} inactive)`);

    // Check if all ads were activated
    if (finalInactiveCount > 0) {
      addStatusMessage(`⚠️ Warning: ${finalInactiveCount} ads are still inactive after activation step.`);

      // Add diagnostic information about inactive ads
      if (finalInactiveCount > 0) {
        const inactiveAdsAfterActivation = refreshedAds.filter(ad => !ad?.isActive);
        const sampleSize = Math.min(5, inactiveAdsAfterActivation.length);

        addStatusMessage(`Sample of inactive ads after activation (${sampleSize}):`);
        for (let i = 0; i < sampleSize; i++) {
          const ad = inactiveAdsAfterActivation[i];
          addStatusMessage(`  - ID: ${ad.id}, Name: ${ad.bannerName}, PageType: ${ad.pageType}`);
        }

        // Check if activation made any difference
        if (attempt1ActiveCount === finalActiveCount && attempt1InactiveCount === finalInactiveCount) {
          addStatusMessage(`⚠️ Critical: Activation process did not change the active/inactive counts at all!`);
          addStatusMessage(`This may indicate an issue with the backend activation API or permissions.`);
        }
      }
    } else {
      addStatusMessage(`✅ All advertisements are now active!`);
    }

    return refreshedAds;
  } catch (err) {
    addStatusMessage(`❌ Error activating ads: ${(err as Error).message}`);
    throw err;
  }
}

/**
 * Run the complete advertisement operation sequence
 */
export async function runCompleteAdSequence(
  options: OperationSequenceOptions,
  setCurrentStep: (step: number) => void,
  setBackendFetched?: (fetched: boolean) => void,
  onComplete?: () => void
): Promise<void> {
  const { addStatusMessage } = options;

  try {
    // Step 1: Fetch Ads
    setCurrentStep(1);
    addStatusMessage("Step 1: Fetching advertisements...");
    await fetchAdsStep(options);

    // Step 2: Clear Ads
    setCurrentStep(2);
    addStatusMessage("Step 2: Clearing existing advertisements...");
    await clearAdsStep(options);

    // Step 3: Fetch again after clearing
    setCurrentStep(3);
    addStatusMessage("Step 3: Fetching after clearing...");
    await fetchAfterClearStep(options);

    // Step 4: Add Ads
    setCurrentStep(4);
    addStatusMessage("Step 4: Adding advertisements...");
    const adsToAddCount = await addAdsStep(options);

    // Step 5: Fetch again after adding
    setCurrentStep(5);
    addStatusMessage("Step 5: Fetching after adding...");
    await fetchAfterAddStep({
      ...options,
      adsToAddCount
    });

    // Step 6: Activate Ads
    setCurrentStep(6);
    addStatusMessage("Step 6: Activating advertisements...");
    await activateAdsStep(options);

    // Make sure backend fetched flag is set to true
    if (setBackendFetched) {
      setBackendFetched(true);
    }

    // Call onComplete callback to refresh local ads if provided
    if (onComplete) {
      try {
        addStatusMessage("Refreshing local data...");
        await Promise.resolve(onComplete());
        addStatusMessage("✅ Local data refreshed successfully");
      } catch (refreshErr) {
        addStatusMessage(`❌ Error refreshing local data: ${(refreshErr as Error).message}`);
      }
    }

    addStatusMessage("✅ All operations completed");
    setCurrentStep(0);
  } catch (err) {
    throw err;
  }
}
