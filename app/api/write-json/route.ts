// app/api/write-json/route.ts
import { writeFile } from "fs/promises";

export async function POST(request: Request) {
  const data = await request.json();

  try {
    await writeFile(
      "path/to/file.json",
      JSON.stringify(data, null, 2),
      "utf-8"
    );
    return new Response(JSON.stringify({ status: "ok" }), { status: 200 });
  } catch (error) {
    return new Response(JSON.stringify({ error: error as Error || "Failed to write file" }), {
      status: 500,
    });
  }
}
