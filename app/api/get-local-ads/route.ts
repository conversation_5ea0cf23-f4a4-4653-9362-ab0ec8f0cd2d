// app/api/get-local-ads/route.ts
import { NextRequest } from "next/server";
import fs from "fs";
import path from "path";

export async function POST(req: NextRequest) {
  const { portal } = await req.json();

  if (!portal) {
    return new Response(JSON.stringify({ error: "Missing portal parameter" }), {
      status: 400,
    });
  }

  try {
    const filePath = path.join(
      process.cwd(),
      `app/json/portal-specific/${portal}/cms-ads-${portal}-visual.json`
    );

    // Check if the file exists
    if (!fs.existsSync(filePath)) {
      return new Response(
        JSON.stringify({
          error: `No local JSON file found for portal: ${portal}`,
          exists: false,
        }),
        { status: 404 }
      );
    }

    // Read and parse the JSON file
    const rawData = fs.readFileSync(filePath, "utf8");
    const adsData = JSON.parse(rawData);

    return new Response(
      JSON.stringify({ success: true, ads: adsData, exists: true }),
      { status: 200 }
    );
  } catch (err) {
    return new Response(
      JSON.stringify({
        error: (err as Error).message || `Failed to load ads for ${portal}`,
        exists: false,
      }),
      { status: 500 }
    );
  }
}
