import { NextResponse } from "next/server";
import fs from "fs";
import path from "path";

export async function GET() {
  try {
    const rawZonesDir = path.join(process.cwd(), "app/json/raw-zones");
    const files = fs.readdirSync(rawZonesDir)
      .filter(file => file.endsWith(".json"))
      .map(file => ({
        name: file,
        path: `app/json/raw-zones/${file}`
      }));

    return NextResponse.json({ files });
  } catch (error) {
    console.error("Error reading raw-zones directory:", error);
    return NextResponse.json(
      { error: "Failed to read JSON files" },
      { status: 500 }
    );
  }
}
