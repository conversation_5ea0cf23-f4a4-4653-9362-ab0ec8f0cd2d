// app/api/ads/add/route.ts
import { getProtocolByEnv } from "@/app/utils/protocol-by-environment.util";
import { NextRequest } from "next/server";

export async function POST(req: NextRequest) {
  const { token, portal, url, advert, environment } = await req.json();
  if (!token || !portal || !url || !advert) {
    return new Response(JSON.stringify({
      success: false,
      error: "Missing required parameters"
    }), { status: 400 });
  }

  try {
    const apiUrl = `${getProtocolByEnv(environment)}://${url}/api/hu/hu/portal/commercial`;

    const res = await fetch(
      apiUrl,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Auth-Token": token,
          Portal: portal,
        },
        body: JSON.stringify(advert),
      }
    );

    // Try to parse the response as JSON
    let result;
    try {
      result = await res.json();
    } catch {
      return new Response(JSON.stringify({
        success: false,
        error: "Invalid JSON response from backend",
        status: res.status,
        statusText: res.statusText
      }), { status: 500 });
    }

    // Check if the response was successful
    if (!res.ok) {
      return new Response(JSON.stringify({
        success: false,
        error: result.error || `Backend returned status ${res.status}`,
        result,
        status: res.status,
        statusText: res.statusText
      }), { status: res.status });
    }

    return new Response(JSON.stringify({ success: true, result }), {
      status: res.status,
    });
  } catch (error) {
    return new Response(
      JSON.stringify({
        success: false,
        error: (error as Error)?.message || "Request failed"
      }),
      {
        status: 500,
      }
    );
  }
}
