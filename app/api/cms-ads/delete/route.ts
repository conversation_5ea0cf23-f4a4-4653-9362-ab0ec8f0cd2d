// app/api/ads/delete/route.ts
import { getProtocolByEnv } from "@/app/utils/protocol-by-environment.util";
import { NextRequest } from "next/server";

export async function POST(req: NextRequest) {
  const { token, portal, url, adId, environment } = await req.json();
  if (!token || !portal || !url || !adId) {
    return new Response(
      JSON.stringify({
        success: false,
        error: "Missing required parameters"
      }),
      { status: 400 }
    );
  }

  try {
    const res = await fetch(
      `${getProtocolByEnv(
        environment
      )}://${url}/api/hu/hu/portal/commercial/${adId}/delete`,
      {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          "X-Auth-Token": token,
          Portal: portal,
        },
      }
    );

    // Try to parse the response as JSON
    let result;
    try {
      result = await res.json();
    } catch {
      return new Response(JSON.stringify({
        success: false,
        error: "Invalid JSON response from backend"
      }), { status: 500 });
    }

    // Check if the response was successful
    if (!res.ok) {
      return new Response(JSON.stringify({
        success: false,
        error: result.error || `Backend returned status ${res.status}`
      }), { status: res.status });
    }

    return new Response(JSON.stringify({ success: true, result }), {
      status: 200,
    });
  } catch (error) {
    return new Response(
      JSON.stringify({
        success: false,
        error: (error as Error)?.message || "Request failed"
      }),
      {
        status: 500,
      }
    );
  }
}
