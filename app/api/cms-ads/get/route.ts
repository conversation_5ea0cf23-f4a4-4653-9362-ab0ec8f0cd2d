// app/api/cms-ads/get/route.ts
import { getProtocolByEnv } from "@/app/utils/protocol-by-environment.util";
import { NextRequest } from "next/server";

const PAGE_LIMIT = 1500;

export async function POST(req: NextRequest) {
  const { token, portal, url, environment } = await req.json();
  if (!token || !portal || !url) {
    return new Response(
      JSON.stringify({
        error: "Missing required parameters",
        success: false
      }),
      { status: 400 }
    );
  }

  try {
    const apiUrl = `${getProtocolByEnv(
      environment
    )}://${url}/api/hu/hu/portal/commercials?page_limit=0&offset_limit=0&rowCount_limit=${PAGE_LIMIT}`;

    const res = await fetch(
      apiUrl,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "X-Auth-Token": token,
          Portal: portal,
        },
      }
    );

    if (!res.ok) {
      return new Response(
        JSON.stringify({
          error: `Error fetching backend ads: ${res.status} ${res.statusText}`,
          success: false
        }),
        { status: res.status }
      );
    }

    const json = await res.json();

    if (!json.data || !Array.isArray(json.data)) {
      return new Response(
        JSON.stringify({
          error: "Invalid response format from backend",
          success: false
        }),
        { status: 500 }
      );
    }

    const ads = json.data;

    return new Response(JSON.stringify({ success: true, ads }), {
      status: 200,
    });
  } catch (error) {
    return new Response(
      JSON.stringify({
        error: (error as Error)?.message || "Request failed",
        success: false
      }),
      {
        status: 500,
      }
    );
  }
}
