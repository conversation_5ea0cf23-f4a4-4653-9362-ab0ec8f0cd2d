import { getProtocolByEnv } from "@/app/utils/protocol-by-environment.util";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const { email, password, portal, environment } = await request.json();

    // Call your backend API with the portal parameter
    const backendResponse = await fetch(
      `${getProtocolByEnv(environment)}://${portal.api}/api/login_check`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Portal: portal.portal,
        },
        body: JSON.stringify({
          email,
          password,
        }),
      }
    );

    if (!backendResponse.ok) {
      const errorData = await backendResponse.json();
      throw new Error(errorData.message || "Backend authentication failed");
    }

    const { token } = await backendResponse.json();

    // Return token and optionally the portal to frontend
    return NextResponse.json(
      {
        token,
      },
      { status: 200 }
    );
  } catch (error) {
    return NextResponse.json(
      { message: (error as Error).message || "Authentication failed" },
      { status: 401 }
    );
  }
}
