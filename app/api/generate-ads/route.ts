export const runtime = "nodejs";

import { NextRequest } from "next/server";
import path from "path";
import fs from "fs";

import { processPortalAds } from "@/app/utils/process-portal-ads.util";
import { RawAdsJson } from "@/app/utils/convert-from-raw-advert.util";

export async function POST(req: NextRequest) {
  const body = await req.json();
  const { portal, jsonFile, jsonIdentifier } = body;

  if (!portal) {
    return new Response(
      JSON.stringify({ error: "Portal parameter is required" }),
      { status: 400 }
    );
  }

  if (!jsonFile) {
    return new Response(
      JSON.stringify({ error: "JSON file parameter is required" }),
      { status: 400 }
    );
  }

  try {
    // Construct the path to the selected JSON file
    const jsonFilePath = path.join(
      process.cwd(),
      "app/json/raw-zones",
      jsonFile
    );

    // Check if the file exists
    if (!fs.existsSync(jsonFilePath)) {
      return new Response(
        JSON.stringify({ error: `File not found: ${jsonFile}` }),
        { status: 404 }
      );
    }

    // Read and parse the JSON file
    const rawData = fs.readFileSync(jsonFilePath, "utf8");
    const rawAdsData = JSON.parse(rawData) as RawAdsJson[];

    // Process the ads using the selected JSON file
    const adverts = processPortalAds({
      jsonIdentifier,
      portal,
      rawAdsData,
      outputPathPrefix: `app/json/portal-specific/${portal}`,
    });

    return new Response(JSON.stringify({ success: true, adverts }), {
      status: 200,
    });
  } catch (err) {
    // Error processing ads
    return new Response(
      JSON.stringify({
        error: (err as Error).message || "Failed to process ads",
      }),
      {
        status: 500,
      }
    );
  }
}
