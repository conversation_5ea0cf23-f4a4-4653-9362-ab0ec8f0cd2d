// app/api/cms-ads/from-local/route.ts
import { NextRequest } from "next/server";

export async function POST(req: NextRequest) {
  const { portal } = await req.json();

  if (!portal) {
    return new Response(JSON.stringify({ error: "Missing portal param" }), {
      status: 400,
    });
  }

  try {
    console.log(`Loading JSON data for portal: ${portal}`);
    const adsModule = await import(
      `@/app/json/portal-specific/${portal}/cms-ads-${portal}.json`
    );

    // Check if the data is in the expected format
    if (!adsModule.default || !Array.isArray(adsModule.default)) {
      console.error(`Invalid JSON data format for portal ${portal}:`, adsModule.default);
      return new Response(
        JSON.stringify({
          error: `Invalid JSON data format for portal ${portal}. Expected an array.`,
        }),
        { status: 500 }
      );
    }

    console.log(`Successfully loaded ${adsModule.default.length} ads for portal: ${portal}`);
    return new Response(JSON.stringify(adsModule.default), { status: 200 });
  } catch (err) {
    return new Response(
      JSON.stringify({
        error: (err as Error).message || `Failed to load ads for ${portal}`,
      }),
      { status: 500 }
    );
  }
}
