"use client";

import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface ChunkingOptionsProps {
  chunkSize: number;
  setChunkSize: (size: number) => void;
  delay: number;
  setDelay: (delay: number) => void;
  idPrefix?: string;
  chunkSizeOptions?: number[];
  delayOptions?: number[];
}

export default function ChunkingOptions({
  chunkSize,
  setChunkSize,
  delay,
  setDelay,
  idPrefix = "default",
  chunkSizeOptions = [100, 200, 500],
  delayOptions = [0, 5000, 10000, 15000, 30000],
}: ChunkingOptionsProps) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
      <div className="space-y-2">
        <Label htmlFor={`${idPrefix}ChunkSize`}>Chunk Size</Label>
        <Select
          value={chunkSize.toString()}
          onValueChange={(value) => setChunkSize(Number(value))}
        >
          <SelectTrigger id={`${idPrefix}ChunkSize`}>
            <SelectValue placeholder="Select chunk size" />
          </SelectTrigger>
          <SelectContent>
            {chunkSizeOptions.map((size) => (
              <SelectItem key={size} value={size.toString()}>
                {size}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor={`${idPrefix}Delay`}>Delay Between Chunks</Label>
        <Select
          value={delay.toString()}
          onValueChange={(value) => setDelay(Number(value))}
        >
          <SelectTrigger id={`${idPrefix}Delay`}>
            <SelectValue placeholder="Select delay" />
          </SelectTrigger>
          <SelectContent>
            {delayOptions.map((ms) => (
              <SelectItem key={ms} value={ms.toString()}>
                {ms / 1000} sec
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
