"use client";

import { Dispatch, SetStateAction } from "react";
import { BackendAdvertisement } from "../definitions/adverts.definitions";
import { PortalOption } from "../definitions/app.types";
import { useAdvertisementOperations } from "../hooks/useAdvertisementOperations";
import OperationButton from "./OperationButton";

interface ClearAllAdsButtonProps {
  selectedPortal: PortalOption;
  token: string;
  setFetchedAds: Dispatch<SetStateAction<BackendAdvertisement[]>>;
  fetchedAds?: BackendAdvertisement[];
  environment: string;
}

export default function ClearAllAdsButton({
  selectedPortal,
  token,
  setFetchedAds,
  fetchedAds,
  environment,
}: ClearAllAdsButtonProps) {
  const { clearAds } = useAdvertisementOperations(
    selectedPortal,
    token,
    environment,
    fetchedAds,
    setFetchedAds
  );

  return (
    <OperationButton
      type="clear"
      label="Clear All Ads"
      processingLabel="Clearing..."
      selectedPortal={selectedPortal}
      token={token}
      environment={environment}
      variant="destructive"
      showChunkingOptions={true}
      operation={async ({ chunkSize, delay, setStatus }) => {
        await clearAds(chunkSize, delay, setStatus);
      }}
    />
  );
}
