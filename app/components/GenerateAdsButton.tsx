"use client";

import { useEffect, useState } from "react";
import { BackendAdvertisement } from "../definitions/adverts.definitions";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { apiClient } from "../utils/api-client";

interface JsonFile {
  name: string;
  path: string;
}

interface GenerateAdsButtonProps {
  portal: string;
  jsonIdentifier: string;
  onGenerate?: () => void;
}

export default function GenerateAdsButton({
  portal,
  jsonIdentifier,
  onGenerate,
}: GenerateAdsButtonProps) {
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState("");
  const [isError, setIsError] = useState(false);
  const [ads, setAds] = useState<BackendAdvertisement[]>([]);
  const [jsonFiles, setJsonFiles] = useState<JsonFile[]>([]);
  const [selectedJsonFile, setSelectedJsonFile] = useState<string>("");

  // Fetch available JSON files on component mount
  useEffect(() => {
    const fetchJsonFiles = async () => {
      try {
        const files = await apiClient.getJsonFiles();
        setJsonFiles(files);

        // Set the first file as default if available
        if (files.length > 0) {
          setSelectedJsonFile(files[0].name);
        }
      } catch (error) {
        setStatus(`Failed to load JSON files: ${(error as Error).message}`);
        setIsError(true);
      }
    };

    fetchJsonFiles();
  }, []);

  const handleClick = async () => {
    if (!selectedJsonFile) {
      setStatus("Please select a JSON file first");
      setIsError(true);
      return;
    }

    setLoading(true);
    setStatus("");
    setIsError(false);
    setAds([]);

    try {
      const generatedAds = await apiClient.generateAds(
        portal,
        jsonIdentifier,
        selectedJsonFile
      );

      setStatus("Ads generated successfully.");
      setAds(generatedAds);

      // Call the onGenerate callback if provided
      if (onGenerate) {
        onGenerate();
      }
    } catch (error) {
      setIsError(true);
      setStatus("Error: " + (error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="jsonFileSelect">Select JSON file</Label>
        <Select
          value={selectedJsonFile}
          onValueChange={setSelectedJsonFile}
          disabled={jsonFiles.length === 0}
        >
          <SelectTrigger id="jsonFileSelect" className="w-full max-w-md">
            <SelectValue placeholder="Select a JSON file" />
          </SelectTrigger>
          <SelectContent>
            {jsonFiles.map((file) => (
              <SelectItem key={file.name} value={file.name}>
                {file.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <Button
        onClick={handleClick}
        disabled={loading || !selectedJsonFile}
        variant="default"
        className="w-full sm:w-auto"
      >
        {loading ? "Generating..." : "Generate Ads"}
      </Button>

      {status && (
        <Alert variant={isError ? "destructive" : "default"}>
          <AlertDescription>{status}</AlertDescription>
        </Alert>
      )}

      {ads.length > 0 && (
        <Alert variant="default" className="bg-blue-50 border-blue-200">
          <AlertDescription className="font-medium text-black">
            Total number of generated ads: {ads.length}
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
