"use client";

import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { ReactNode } from "react";

interface SectionCardProps {
  number: number;
  title: string;
  description: string;
  children: ReactNode;
}

/**
 * A reusable card component for displaying numbered sections with title, description, and content
 */
export default function SectionCard({
  number,
  title,
  description,
  children,
}: SectionCardProps) {
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>{number}. {title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>{children}</CardContent>
      </Card>
    </div>
  );
}
