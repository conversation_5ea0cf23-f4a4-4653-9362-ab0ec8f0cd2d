"use client";

import { BackendAdvertisement } from "../definitions/adverts.definitions";
import { PortalOption } from "../definitions/app.types";
import { useAdvertisementOperations } from "../hooks/useAdvertisementOperations";
import { ConvertedAdItem } from "../utils/convert-to-cms-advert.util";
import OperationButton from "./OperationButton";

interface AddAdsButtonProps {
  selectedPortal: PortalOption;
  token: string;
  environment: string;
  onComplete?: () => void;
  fetchedAds?: BackendAdvertisement[];
  setFetchedAds?: React.Dispatch<React.SetStateAction<BackendAdvertisement[]>>;
}

export default function AddAdsButton({
  selectedPortal,
  token,
  environment,
  onComplete,
  fetchedAds,
  setFetchedAds,
}: AddAdsButtonProps) {
  const { addAds } = useAdvertisementOperations(
    selectedPortal,
    token,
    environment,
    fetchedAds,
    setFetchedAds || (() => {})
  );

  return (
    <OperationButton
      type="add"
      label="Add Adverts"
      processingLabel="Adding..."
      selectedPortal={selectedPortal}
      token={token}
      environment={environment}
      showChunkingOptions={true}
      onComplete={onComplete}
      operation={async ({ chunkSize, delay, setStatus, setIsError }) => {
        try {
          setStatus("Reading local adverts...");
          const res = await fetch("/api/get-json", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ portal: selectedPortal.portal }),
          });

          if (!res.ok) {
            throw new Error("Failed to get local JSON data");
          }

          const data = await res.json();

          // Check if the data is in the expected format
          if (!Array.isArray(data)) {
            throw new Error("Invalid data format from /api/get-json. Expected an array.");
          }

          const localAds: ConvertedAdItem[] = data;

          // Process ads in chunks, only adding those that don't exist in the backend
          await addAds(
            localAds,
            chunkSize,
            delay,
            setStatus,
            (total, filtered) => {
              setStatus(`Found ${total} local ads, ${filtered} are missing from backend and will be added.`);
            }
          );
        } catch (err) {
          setIsError(true);
          setStatus("Error: " + (err as Error).message);
          throw err;
        }
      }}
    />
  );
}
