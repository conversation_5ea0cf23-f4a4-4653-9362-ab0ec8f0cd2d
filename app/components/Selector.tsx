"use client";

import React from "react";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";

interface SelectorOption {
  value: string;
  label: string;
  portal?: string;
  api?: string;
}

interface SelectorProps {
  options: SelectorOption[];
  selectedValue: string;
  onSelect: (value: string) => void;
  label?: string;
  className?: string;
  disabled?: boolean;
}

export function Selector({
  options,
  selectedValue,
  onSelect,
  label,
  className = "",
  disabled = false,
}: SelectorProps) {
  const handleValueChange = (value: string) => {
    onSelect(value);
  };

  return (
    <div className={cn("flex flex-col gap-2", className)}>
      {label && <Label htmlFor={label}>{label}</Label>}
      <Select
        value={selectedValue}
        onValueChange={handleValueChange}
        disabled={disabled}
      >
        <SelectTrigger id={label} className="w-full">
          <SelectValue placeholder="Select an option" />
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem
              key={option.value ?? option.portal}
              value={option.value ?? option.portal}
            >
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
