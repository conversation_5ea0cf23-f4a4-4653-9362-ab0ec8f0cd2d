import { Alert, AlertDescription } from "@/components/ui/alert";
import { BackendAdvertisement } from "../definitions/adverts.definitions";
import {
  findMatchingServerAd,
  existsInBackendByBasicCriteria,
  findServerAdByBasicCriteria,
} from "../utils/advertisement-helpers.util";

interface AdvertisementTableCountProps {
  localAds: BackendAdvertisement[];
  serverAds: BackendAdvertisement[];
  backendFetched: boolean;
}

function AdvertisementTableCount({
  localAds,
  serverAds,
  backendFetched
}: AdvertisementTableCountProps) {
  // Count ads with different issues
  const getAdCounts = () => {
    if (!backendFetched || localAds.length === 0) return null;

    const counts = {
      total: localAds.length,
      active: 0,
      inactive: 0,
      missing: 0,
      zoneIssue: 0,
      masterIssue: 0,
      bothIssues: 0,
    };

    localAds.forEach(ad => {
      const matchingServerAd = findMatchingServerAd(ad, serverAds);
      const isInServer = !!matchingServerAd;

      if (isInServer) {
        if (matchingServerAd.isActive) {
          counts.active++;
        } else {
          counts.inactive++;
        }
      } else {
        // Check if an ad with the same name, pagetype, and medium exists in the backend
        const existsWithBasicCriteria = existsInBackendByBasicCriteria(ad, serverAds);

        if (!existsWithBasicCriteria) {
          counts.missing++;
        } else {
          // Find the server ad with matching basic criteria
          const matchingBasicAd = findServerAdByBasicCriteria(ad, serverAds);

          if (matchingBasicAd) {
            // Check if zone ID or master ID are different
            const zoneDiffers =
              (ad.zonaId && !matchingBasicAd.zonaId) ||
              (ad.zonaId && matchingBasicAd.zonaId && ad.zonaId !== matchingBasicAd.zonaId);

            const masterDiffers =
              (ad.masterId && !matchingBasicAd.masterId) ||
              (ad.masterId && matchingBasicAd.masterId && ad.masterId !== matchingBasicAd.masterId);

            if (zoneDiffers && masterDiffers) {
              counts.bothIssues++;
            } else if (zoneDiffers) {
              counts.zoneIssue++;
            } else if (masterDiffers) {
              counts.masterIssue++;
            }
          }
        }
      }
    });

    return counts;
  };

  const counts = getAdCounts();

  if (!counts) return null;

  return (
    <Alert variant="default" className="mt-4 bg-blue-50 border-blue-200">
      <AlertDescription className="font-medium text-black">
        <div className="flex flex-col gap-1">
          <div>Total ads: {counts.total}</div>
          <div className="text-green-700">✅ Active: {counts.active}</div>
          {counts.inactive > 0 && (
            <div className="text-amber-700">
              ⚠️ Inactive: {counts.inactive}
            </div>
          )}
          {counts.missing > 0 && (
            <div className="text-red-700">
              ❌ Missing from backend: {counts.missing}
            </div>
          )}
          {counts.zoneIssue > 0 && (
            <div className="text-amber-700">
              ⚠️ Zone ID issues: {counts.zoneIssue}
            </div>
          )}
          {counts.masterIssue > 0 && (
            <div className="text-amber-700">
              ⚠️ Master ID issues: {counts.masterIssue}
            </div>
          )}
          {counts.bothIssues > 0 && (
            <div className="text-amber-700">
              ⚠️ Both Zone & Master ID issues: {counts.bothIssues}
            </div>
          )}
        </div>
      </AlertDescription>
    </Alert>
  );
}

export default AdvertisementTableCount;
