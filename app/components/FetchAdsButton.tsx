"use client";

import { Dispatch, SetStateAction } from "react";
import { BackendAdvertisement } from "../definitions/adverts.definitions";
import { PortalOption } from "../definitions/app.types";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useAdvertisementOperations } from "../hooks/useAdvertisementOperations";
import OperationButton from "./OperationButton";

interface FetchAdsButtonProps {
  selectedPortal: PortalOption;
  token: string;
  setFetchedAds: Dispatch<SetStateAction<BackendAdvertisement[]>>;
  fetchedAds?: BackendAdvertisement[];
  environment: string;
}

export default function FetchAdsButton({
  selectedPortal,
  token,
  setFetchedAds,
  fetchedAds,
  environment,
}: FetchAdsButtonProps) {
  const { fetchAds } = useAdvertisementOperations(
    selectedPortal,
    token,
    environment,
    fetchedAds,
    setFetchedAds
  );

  return (
    <div className="space-y-4">
      <OperationButton
        type="fetch"
        label="Fetch Ads"
        processingLabel="Fetching..."
        selectedPortal={selectedPortal}
        token={token}
        environment={environment}
        operation={async ({ setStatus, setIsError, setProcessing }) => {
          try {
            setFetchedAds([]);
            const ads = await fetchAds(true);
            setStatus("Ads fetched successfully.");
            setFetchedAds(ads);
          } catch (err) {
            setIsError(true);
            setStatus("Fetch error: " + (err as Error).message);
            throw err;
          } finally {
            setProcessing(false);
          }
        }}
      />

      {fetchedAds && fetchedAds?.length > 0 && (
        <Alert variant="default" className="bg-blue-50 border-blue-200">
          <AlertDescription className="font-medium text-black">
            Total number of fetched ads: {fetchedAds.length}
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
