"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { PortalOption } from "../definitions/app.types";
import ChunkingOptions from "./ChunkingOptions";
import StatusMessage from "./StatusMessage";

export type OperationType = "fetch" | "clear" | "add" | "activate" | "generate" | "doAll";

interface OperationButtonProps {
  type: OperationType;
  label: string;
  processingLabel: string;
  selectedPortal: PortalOption;
  token: string;
  environment: string;
  onComplete?: () => void;
  showChunkingOptions?: boolean;
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  className?: string;
  operation: (params: {
    portal: string;
    api: string;
    token: string;
    environment: string;
    chunkSize: number;
    delay: number;
    setStatus: (status: string) => void;
    setIsError: (isError: boolean) => void;
    setProcessing: (processing: boolean) => void;
  }) => Promise<void>;
}

export default function OperationButton({
  type,
  label,
  processingLabel,
  selectedPortal: { portal, api },
  token,
  environment,
  onComplete,
  showChunkingOptions = false,
  variant = "default",
  className = "w-full sm:w-auto",
  operation,
}: OperationButtonProps) {
  const [status, setStatus] = useState("");
  const [chunkSize, setChunkSize] = useState(100);
  const [delay, setDelay] = useState(0);
  const [processing, setProcessing] = useState(false);
  const [isError, setIsError] = useState(false);

  const handleOperation = async () => {
    setProcessing(true);
    setStatus(`Starting ${type} operation...`);
    setIsError(false);

    try {
      await operation({
        portal,
        api,
        token,
        environment,
        chunkSize,
        delay,
        setStatus,
        setIsError,
        setProcessing,
      });

      if (onComplete) {
        onComplete();
      }
    } catch (err) {
      setIsError(true);
      setStatus(`Error: ${(err as Error).message}`);
    } finally {
      setProcessing(false);
    }
  };

  return (
    <div className="space-y-4">
      <Button
        onClick={handleOperation}
        disabled={!token || processing}
        variant={variant}
        className={className}
      >
        {processing ? processingLabel : label}
      </Button>

      {showChunkingOptions && (
        <ChunkingOptions
          chunkSize={chunkSize}
          setChunkSize={setChunkSize}
          delay={delay}
          setDelay={setDelay}
          idPrefix={type}
        />
      )}

      <StatusMessage status={status} isError={isError} />
    </div>
  );
}
