"use client";

import { BackendAdvertisement } from "../definitions/adverts.definitions";
import {
  findMatchingServerAd,
  existsInBackendByBasicCriteria,
  findServerAdByBasicCriteria,
  valuesDiffer,
  rowHasDifferences,
} from "../utils/advertisement-helpers.util";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { cn } from "@/lib/utils";
import AdvertisementTableCount from "./AdvertisementTableCount";

interface AdvertisementTableProps {
  localAds: BackendAdvertisement[];
  serverAds: BackendAdvertisement[];
  backendFetched?: boolean;
}

export default function AdvertisementTable({
  localAds,
  serverAds,
  backendFetched = false,
}: AdvertisementTableProps) {
  // Status text constants with emojis
  const STATUS_TEXT = {
    NOT_FETCHED: "ℹ️ Backend not fetched yet",
    ACTIVE: "✅ Active in Backend",
    INACTIVE: "⚠️ Inactive in Backend",
    MISSING: "❌ Missing from Backend",
    DIFFERENT_ZONE: "⚠️ Different Zone ID",
    DIFFERENT_MASTER: "⚠️ Different Master ID",
    DIFFERENT_BOTH: "⚠️ Different Zone & Master IDs",
  };
  return (
    <div className="space-y-4">
      <div>
        {/* <h2 className="text-2xl font-bold mb-2">Advertisement Status Table</h2> */}
        <p className="text-sm text-muted-foreground">
          This table shows the status of each advertisement from the local JSON
          file compared to the backend.
        </p>

        {!backendFetched && (
          <Alert variant="default" className="mt-4">
            <AlertDescription>
              <strong>Note:</strong> Backend data has not been fetched yet. Log
              in and use the &quot;Fetch Ads&quot; button to retrieve backend
              data.
            </AlertDescription>
          </Alert>
        )}

        {backendFetched && serverAds.length === 0 && (
          <Alert variant="default" className="mt-4">
            <AlertDescription>
              <strong>Note:</strong> No advertisements found in the backend.
            </AlertDescription>
          </Alert>
        )}

        {backendFetched && serverAds.length > 0 && (
          <>
            {/* Ad counts alert */}
            {localAds.length > 0 && (
              <AdvertisementTableCount
                localAds={localAds}
                serverAds={serverAds}
                backendFetched={backendFetched}
              />
            )}

            {/* Legend */}
            <div className="mt-4 p-4 bg-muted rounded-md text-sm">
              <strong className="block mb-2">Legend:</strong>
              <ul className="space-y-1 list-disc pl-5">
                <li>
                  When differences are found, both local and backend values are
                  shown
                </li>
                <li>
                  <Badge variant="default" className="font-normal">
                    {STATUS_TEXT.ACTIVE}
                  </Badge>{" "}
                  is only shown when ad params are exactly the same in the backend
                </li>
                <li>
                  <Badge variant="destructive" className="font-normal">
                    {STATUS_TEXT.MISSING}
                  </Badge>{" "}
                  is only shown when no ad with matching name, pagetype, and
                  medium exists in the backend
                </li>
                <li>
                  <Badge
                    variant="secondary"
                    className="bg-amber-100 text-amber-800 border-amber-200 font-normal"
                  >
                    {STATUS_TEXT.DIFFERENT_ZONE}
                  </Badge>{" "}
                  indicates an ad with the same name, pagetype, and medium exists
                  but has a different Zone ID
                </li>
                <li>
                  <Badge
                    variant="secondary"
                    className="bg-amber-100 text-amber-800 border-amber-200 font-normal"
                  >
                    {STATUS_TEXT.DIFFERENT_MASTER}
                  </Badge>{" "}
                  indicates an ad with the same name, pagetype, and medium exists
                  but has a different Master ID
                </li>
                <li>
                  <Badge
                    variant="secondary"
                    className="bg-amber-100 text-amber-800 border-amber-200 font-normal"
                  >
                    {STATUS_TEXT.DIFFERENT_BOTH}
                  </Badge>{" "}
                  indicates an ad with the same name, pagetype, and medium exists
                  but has different Zone and Master IDs
                </li>
              </ul>
            </div>
          </>
        )}
      </div>

      {localAds.length > 0 ? (
        <div className="overflow-x-auto w-full">
          <Table className="w-full table-fixed">
            <TableHeader>
              <TableRow>
                <TableHead className="w-[5%]">#</TableHead>
                <TableHead className="w-[25%]">Banner Name</TableHead>
                <TableHead className="w-[5%]">Medium</TableHead>
                <TableHead className="w-[20%]">Page Type</TableHead>
                <TableHead className="w-[30%]">Master ID</TableHead>
                <TableHead className="w-[30%]">Zone ID</TableHead>
                <TableHead className="w-[5%]">Is Adult</TableHead>
                <TableHead className="w-[15%]">Status</TableHead>
              </TableRow>
            </TableHeader>
          <TableBody>
            {localAds.map((ad, index) => {
              const matchingServerAd = findMatchingServerAd(ad, serverAds);
              const isInServer = !!matchingServerAd;
              const isActive = matchingServerAd?.isActive || false;

              // Default status for when backend is not fetched
              let statusText = STATUS_TEXT.NOT_FETCHED;
              let statusVariant:
                | "default"
                | "destructive"
                | "secondary"
                | "outline" = "secondary";

              // Only show status based on backend data if it has been fetched
              if (backendFetched) {
                if (isInServer) {
                  if (isActive) {
                    statusText = STATUS_TEXT.ACTIVE;
                    statusVariant = "default";
                  } else {
                    statusText = STATUS_TEXT.INACTIVE;
                    statusVariant = "secondary";
                  }
                } else {
                  // Check if an ad with the same name, pagetype, and medium exists in the backend
                  const existsWithBasicCriteria =
                    existsInBackendByBasicCriteria(ad, serverAds);

                  if (!existsWithBasicCriteria) {
                    // Only show "Missing from Backend" if no ad with matching name, pagetype, and medium is found
                    statusText = STATUS_TEXT.MISSING;
                    statusVariant = "destructive";
                  } else {
                    // Find the server ad with matching basic criteria
                    const matchingBasicAd = findServerAdByBasicCriteria(
                      ad,
                      serverAds
                    );

                    if (matchingBasicAd) {
                      // Check if zone ID or master ID are different
                      const zoneDiffers =
                        (ad.zonaId && !matchingBasicAd.zonaId) ||
                        (ad.zonaId &&
                          matchingBasicAd.zonaId &&
                          ad.zonaId !== matchingBasicAd.zonaId);

                      const masterDiffers =
                        (ad.masterId && !matchingBasicAd.masterId) ||
                        (ad.masterId &&
                          matchingBasicAd.masterId &&
                          ad.masterId !== matchingBasicAd.masterId);

                      if (zoneDiffers || masterDiffers) {
                        // If zone ID or master ID differ, show that in the status
                        if (zoneDiffers && masterDiffers) {
                          statusText = STATUS_TEXT.DIFFERENT_BOTH;
                        } else if (zoneDiffers) {
                          statusText = STATUS_TEXT.DIFFERENT_ZONE;
                        } else if (masterDiffers) {
                          statusText = STATUS_TEXT.DIFFERENT_MASTER;
                        }
                        statusVariant = "outline";
                      } else {
                        // If there's a basic match but not an exact match for other reasons
                        statusText = STATUS_TEXT.ACTIVE;
                        statusVariant = "default";
                      }
                    } else {
                      // Fallback (shouldn't happen since we already checked existsWithBasicCriteria)
                      statusText = STATUS_TEXT.ACTIVE;
                      statusVariant = "default";
                    }
                  }
                }
              }

              const hasDifferences =
                isInServer && rowHasDifferences(ad, matchingServerAd);

              return (
                <TableRow
                  key={index}
                  className={cn(hasDifferences && "bg-amber-50")}
                >
                  <TableCell className="text-center">{index + 1}</TableCell>
                  <TableCell className="truncate" title={ad.bannerName}>
                    <div className="max-w-full overflow-hidden text-ellipsis">
                      {ad.bannerName}
                    </div>
                  </TableCell>
                  <TableCell className="text-center">{ad.medium}</TableCell>
                  <TableCell className="truncate" title={ad.pageType}>
                    <div className="max-w-full overflow-hidden text-ellipsis">
                      {ad.pageType}
                    </div>
                  </TableCell>
                  <TableCell>
                    {isInServer &&
                    valuesDiffer(ad.masterId, matchingServerAd?.masterId) ? (
                      <div>
                        <span className="bg-amber-50 border border-amber-200 rounded px-2 py-0.5 inline-block max-w-full overflow-hidden text-ellipsis" title={ad.masterId}>
                          {ad.masterId}
                        </span>
                        <span className="text-xs text-amber-700 mt-1 block truncate" title={matchingServerAd?.masterId || "N/A"}>
                          Backend: {matchingServerAd?.masterId || "N/A"}
                        </span>
                      </div>
                    ) : (
                      <div className="truncate" title={ad.masterId}>
                        {ad.masterId}
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    {isInServer &&
                    valuesDiffer(ad.zonaId, matchingServerAd?.zonaId) ? (
                      <div>
                        <span className="bg-amber-50 border border-amber-200 rounded px-2 py-0.5 inline-block max-w-full overflow-hidden text-ellipsis" title={ad.zonaId}>
                          {ad.zonaId}
                        </span>
                        <span className="text-xs text-amber-700 mt-1 block truncate" title={matchingServerAd?.zonaId || "N/A"}>
                          Backend: {matchingServerAd?.zonaId || "N/A"}
                        </span>
                      </div>
                    ) : (
                      <div className="truncate" title={ad.zonaId}>
                        {ad.zonaId}
                      </div>
                    )}
                  </TableCell>
                  <TableCell className="text-center">{ad.isAdultAd ? "Yes" : "No"}</TableCell>
                  <TableCell>
                    <div className="flex flex-col space-y-1">
                      <Badge
                        variant={
                          statusVariant === "outline"
                            ? "secondary"
                            : statusVariant
                        }
                        className={cn(
                          statusVariant === "outline" &&
                            "bg-amber-100 text-amber-800 border-amber-200",
                          "font-medium inline-block w-fit"
                        )}
                      >
                        {statusText}
                      </Badge>

                      {isInServer && matchingServerAd?.id && (
                        <div className="text-xs text-muted-foreground truncate" title={`ID: ${matchingServerAd.id}`}>
                          ID: {matchingServerAd.id}
                        </div>
                      )}

                      {!isInServer && statusText.includes("Zone") && (
                        <div className="text-xs text-amber-700 truncate"
                          title={`Backend Zone ID: ${findServerAdByBasicCriteria(ad, serverAds)?.zonaId || "N/A"}`}>
                          Backend Zone ID:{" "}
                          {findServerAdByBasicCriteria(ad, serverAds)?.zonaId ||
                            "N/A"}
                        </div>
                      )}

                      {!isInServer &&
                        statusText.includes("Master") &&
                        ad?.masterId && (
                          <div className="text-xs text-amber-700 truncate"
                            title={`Backend Master ID: ${findServerAdByBasicCriteria(ad, serverAds)?.masterId || "N/A"}`}>
                            Backend Master ID:{" "}
                            {findServerAdByBasicCriteria(ad, serverAds)
                              ?.masterId || "N/A"}
                          </div>
                        )}
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
          </Table>
        </div>
      ) : (
        <Alert>
          <AlertDescription>
            No local advertisements found for this portal.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
