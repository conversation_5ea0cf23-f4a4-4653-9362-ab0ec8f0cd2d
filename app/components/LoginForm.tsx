"use client"; // Mark as Client Component

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { Dispatch, FormEvent, SetStateAction, useState } from "react";

interface LoginFormProps {
  selectedPortal: { label: string; api: string; portal: string };
  setAuthToken: Dispatch<SetStateAction<string>>;
  authToken?: string;
  environment: string;
}

export default function LoginForm({
  selectedPortal,
  setAuthToken,
  authToken,
  environment,
}: LoginFormProps) {
  const [email, setEmail] = useState("<EMAIL>");
  const [password, setPassword] = useState("qwertz");
  const [tokenInput, setTokenInput] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [useTokenInput, setUseTokenInput] = useState(false);

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      // Call our Next.js API route (middleware)
      const response = await fetch("/api/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email,
          password,
          portal: selectedPortal,
          environment,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Login failed");
      }

      // Store token (you might want to use secure cookies instead)
      if (typeof window !== "undefined") {
        setAuthToken(data.token);
      }
    } catch (err) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      setError((err as any).message);
    } finally {
      setLoading(false);
    }
  };

  const handleSetToken = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!tokenInput.trim()) {
      setError("Token is required");
      return;
    }

    setError("");
    setAuthToken(tokenInput.trim());
  };

  return (
    <Card className="max-w-md mx-auto mt-10">
      <CardHeader>
        <CardTitle className="text-2xl text-center">
          {authToken ? "Logged in" : "Login"} to {selectedPortal.label}
        </CardTitle>
        {error && (
          <CardDescription className="mt-2 p-2 bg-destructive/10 text-destructive rounded">
            {error}
          </CardDescription>
        )}
      </CardHeader>
      {authToken ? (
        <CardContent>
          <div className="text-center p-4">
            <p className="text-green-600 dark:text-green-400 font-medium mb-2">
              ✓ Successfully authenticated
            </p>
            <p className="text-sm text-muted-foreground">
              You can now use the API operations below.
            </p>
          </div>
        </CardContent>
      ) : (
        <>
          <div className="flex justify-center mb-4 px-6">
            <div className="inline-flex rounded-md shadow-sm" role="group">
              <button
                type="button"
                onClick={() => setUseTokenInput(false)}
                className={cn(
                  "px-4 py-2 text-sm font-medium border rounded-l-lg",
                  !useTokenInput
                    ? "bg-primary text-primary-foreground border-primary"
                    : "bg-background text-muted-foreground border-border hover:bg-muted/50"
                )}
              >
                Login Form
              </button>
              <button
                type="button"
                onClick={() => setUseTokenInput(true)}
                className={cn(
                  "px-4 py-2 text-sm font-medium border rounded-r-lg",
                  useTokenInput
                    ? "bg-primary text-primary-foreground border-primary"
                    : "bg-background text-muted-foreground border-border hover:bg-muted/50"
                )}
              >
                Token Input
              </button>
            </div>
          </div>

          {useTokenInput ? (
            // Token input form
            <form onSubmit={handleSetToken}>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label htmlFor="token" className="text-sm font-medium">
                    Token
                  </label>
                  <Input
                    type="text"
                    id="token"
                    value={tokenInput}
                    onChange={(e) => setTokenInput(e.target.value)}
                    placeholder="Enter your authentication token"
                    required
                  />
                  <p className="text-xs text-muted-foreground">
                    Enter your authentication token directly if you already have
                    one.
                    {environment === "prod" && (
                      <span className="block mt-1 text-amber-500 dark:text-amber-400">
                        Note: In production environment, direct login is not
                        possible. Token input is required.
                      </span>
                    )}
                  </p>
                </div>
              </CardContent>
              <CardFooter>
                <Button type="submit" className={cn("w-full", "mt-6")}>
                  Set Token
                </Button>
              </CardFooter>
            </form>
          ) : (
            // Email/Password login form
            <form onSubmit={handleSubmit}>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label htmlFor="email" className="text-sm font-medium">
                    Email
                  </label>
                  <Input
                    type="email"
                    id="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="password" className="text-sm font-medium">
                    Password
                  </label>
                  <Input
                    type="password"
                    id="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  type="submit"
                  disabled={loading}
                  className={cn("w-full", loading && "opacity-70", "mt-6")}
                >
                  {loading ? "Logging in..." : "Login"}
                </Button>
              </CardFooter>
            </form>
          )}
        </>
      )}
    </Card>
  );
}
