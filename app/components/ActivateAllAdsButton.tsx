"use client";

import { Dispatch, SetStateAction } from "react";
import { BackendAdvertisement } from "../definitions/adverts.definitions";
import { PortalOption } from "../definitions/app.types";
import { useAdvertisementOperations } from "../hooks/useAdvertisementOperations";
import { delayMs } from "../utils/ad-operations.util";
import OperationButton from "./OperationButton";

interface ActivateAllAdsButtonProps {
  selectedPortal: PortalOption;
  token: string;
  setFetchedAds: Dispatch<SetStateAction<BackendAdvertisement[]>>;
  fetchedAds?: BackendAdvertisement[];
  environment: string;
}

export default function ActivateAllAdsButton({
  selectedPortal,
  token,
  setFetchedAds,
  fetchedAds,
  environment,
}: ActivateAllAdsButtonProps) {
  const { activateAds, fetchWithRetry } = useAdvertisementOperations(
    selectedPortal,
    token,
    environment,
    fetchedAds,
    setFetchedAds
  );

  return (
    <OperationButton
      type="activate"
      label="Activate All Ads"
      processingLabel="Activating..."
      selectedPortal={selectedPortal}
      token={token}
      environment={environment}
      variant="outline"
      showChunkingOptions={true}
      operation={async ({ chunkSize, delay, setStatus, setIsError }) => {
        try {
          await activateAds(chunkSize, delay, setStatus);

          setStatus("Waiting for backend to update...");

          // Add a longer delay after activation
          await delayMs(5000);

          // Force multiple refreshes to ensure we get the updated state
          const refreshedAds = await fetchWithRetry(
            3,
            2000,
            (attempt, totalAttempts) => {
              setStatus(`Refreshing backend state (attempt ${attempt} of ${totalAttempts})...`);
            }
          );

          // Check if all ads were activated
          const stillInactiveCount = refreshedAds.filter(ad => !ad?.isActive).length;
          if (stillInactiveCount > 0) {
            setStatus(`Warning: ${stillInactiveCount} ads are still inactive after activation. Try fetching again.`);
          } else {
            setStatus("All inactive ads activated successfully!");
          }

          setFetchedAds(refreshedAds);
        } catch (err) {
          setIsError(true);
          setStatus("Error: " + (err as Error).message);
          throw err;
        }
      }}
    />
  );
}
