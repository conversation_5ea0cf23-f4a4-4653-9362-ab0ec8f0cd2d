"use client";

import { Alert, AlertDescription } from "@/components/ui/alert";

interface StatusMessageProps {
  status: string;
  isError?: boolean;
  className?: string;
}

export default function StatusMessage({
  status,
  isError = false,
  className,
}: StatusMessageProps) {
  if (!status) return null;

  return (
    <Alert variant={isError ? "destructive" : "default"} className={className}>
      <AlertDescription>{status}</AlertDescription>
    </Alert>
  );
}

interface DetailedStatusLogProps {
  messages: string[];
  className?: string;
}

export function DetailedStatusLog({
  messages,
  className,
}: DetailedStatusLogProps) {
  if (!messages.length) return null;

  return (
    <div
      className={`mt-4 p-4 border rounded-md bg-muted/30 max-h-60 overflow-y-auto ${
        className || ""
      }`}
    >
      <h4 className="text-sm font-medium mb-2">Operation Log</h4>
      <div className="space-y-1 text-xs">
        {messages.map((message, index) => (
          <div
            key={index}
            className="py-1 border-b border-gray-100 last:border-0"
          >
            {message}
          </div>
        ))}
      </div>
    </div>
  );
}
