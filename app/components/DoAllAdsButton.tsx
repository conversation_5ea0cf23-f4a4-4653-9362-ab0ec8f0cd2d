"use client";

import { useState } from "react";
import { BackendAdvertisement } from "../definitions/adverts.definitions";
import { PortalOption } from "../definitions/app.types";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAdvertisementOperations } from "../hooks/useAdvertisementOperations";
import { runCompleteAdSequence, OperationSequenceOptions } from "../utils/ad-operation-sequence.util";

interface DoAllAdsButtonProps {
  selectedPortal: PortalOption;
  token: string;
  setFetchedAds: React.Dispatch<React.SetStateAction<BackendAdvertisement[]>>;
  fetchedAds?: BackendAdvertisement[];
  environment: string;
  onComplete?: () => void; // Callback to refresh local ads
  setBackendFetched?: React.Dispatch<React.SetStateAction<boolean>>;
}

export default function DoAllAdsButton({
  selectedPortal: { portal, api },
  token,
  setFetchedAds,
  fetchedAds,
  environment,
  onComplete,
  setBackendFetched,
}: DoAllAdsButtonProps) {
  const [status, setStatus] = useState("");
  const [detailedStatus, setDetailedStatus] = useState<string[]>([]);
  const [chunkSize, setChunkSize] = useState(100);
  const [delay, setDelay] = useState(0);
  const [processing, setProcessing] = useState(false);
  const [isError, setIsError] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  // Use the custom hook for fetching ads
  const { fetchAds } = useAdvertisementOperations(
    { portal, api } as PortalOption,
    token,
    environment,
    fetchedAds,
    setFetchedAds
  );

  // Create a function that matches the expected signature for the operation sequence
  const fetchAdsIfNeeded = async (forceRefresh?: boolean) => {
    return await fetchAds(forceRefresh);
  };

  const addStatusMessage = (message: string) => {
    setDetailedStatus((prev) => [...prev, message]);
  };

  const handleDoAll = async () => {
    setProcessing(true);
    setStatus("Starting all operations...");
    setDetailedStatus([]);
    setIsError(false);

    try {
      // Create options for the operation sequence
      const options: OperationSequenceOptions = {
        portal,
        api,
        token,
        environment,
        chunkSize,
        delay,
        addStatusMessage,
        fetchAdsIfNeeded
      };

      // Run the complete ad sequence
      await runCompleteAdSequence(
        options,
        setCurrentStep,
        setBackendFetched,
        onComplete
      );

      setStatus("All operations completed successfully!");
    } catch (err) {
      setIsError(true);
      setStatus(`Error: ${(err as Error).message}`);
    } finally {
      setProcessing(false);
    }
  };

  return (
    <div className="space-y-4">
      <Button
        onClick={handleDoAll}
        disabled={!token || processing}
        variant="default"
        className="w-full"
      >
        {processing ? `Processing (Step ${currentStep}/6)...` : "Do All Operations"}
      </Button>

      {processing && (
        <Alert className="mt-2">
          <AlertDescription className="flex flex-col gap-1">
            <div className="font-medium">
              Processing in chunks of {chunkSize} items with {delay}ms delay between chunks
            </div>
            {currentStep > 0 && (
              <div className="text-sm">
                <span className="font-medium">Current step:</span> {
                  currentStep === 1 ? "Fetching advertisements" :
                  currentStep === 2 ? "Clearing existing advertisements" :
                  currentStep === 3 ? "Verifying clear operation" :
                  currentStep === 4 ? "Adding advertisements" :
                  currentStep === 5 ? "Verifying add operation" :
                  currentStep === 6 ? "Activating advertisements" :
                  "Unknown step"
                }
              </div>
            )}
            {detailedStatus.length > 0 && (
              <div className="text-sm italic">
                Last action: {detailedStatus[detailedStatus.length - 1]}
              </div>
            )}
          </AlertDescription>
        </Alert>
      )}

      <div className="mt-6 border rounded-lg p-4 bg-muted/30">
        <h3 className="text-lg font-medium mb-2">Chunking Configuration</h3>
        <p className="text-sm text-muted-foreground mb-4">
          Configure how advertisements are processed in batches to optimize performance and reliability.
        </p>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="doAllChunkSize" className="flex items-center justify-between">
              <span>Chunk Size</span>
              <span className="text-xs text-muted-foreground">(Ads processed in each batch)</span>
            </Label>
            <Select
              value={chunkSize.toString()}
              onValueChange={(value) => setChunkSize(Number(value))}
            >
              <SelectTrigger id="doAllChunkSize">
                <SelectValue placeholder="Select chunk size" />
              </SelectTrigger>
              <SelectContent>
                {[100, 200, 500].map((size) => (
                  <SelectItem key={size} value={size.toString()}>
                    {size} {size === 100 ? "(Default)" : size === 500 ? "(Maximum)" : ""}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground mt-1">
              Smaller chunks are more reliable but slower. Larger chunks are faster but may cause errors.
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="doAllDelay" className="flex items-center justify-between">
              <span>Delay Between Chunks</span>
              <span className="text-xs text-muted-foreground">(Prevents rate limiting)</span>
            </Label>
            <Select
              value={delay.toString()}
              onValueChange={(value) => setDelay(Number(value))}
            >
              <SelectTrigger id="doAllDelay">
                <SelectValue placeholder="Select delay" />
              </SelectTrigger>
              <SelectContent>
                {[0, 5000, 10000, 15000, 30000].map((ms) => (
                  <SelectItem key={ms} value={ms.toString()}>
                    {ms / 1000} sec {ms === 5000 ? "(Default)" : ms === 0 ? "(Not recommended)" : ""}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground mt-1">
              Longer delays reduce the risk of API rate limiting but increase total processing time.
            </p>
          </div>
        </div>
      </div>

      {status && (
        <Alert variant={isError ? "destructive" : "default"}>
          <AlertDescription>{status}</AlertDescription>
        </Alert>
      )}

      {detailedStatus.length > 0 && (
        <div className="mt-4 p-4 border rounded-md bg-muted/30 max-h-60 overflow-y-auto">
          <h4 className="text-sm font-medium mb-2">Operation Log</h4>
          <div className="space-y-1 text-xs">
            {detailedStatus.map((message, index) => (
              <div key={index} className="py-1 border-b border-gray-100 last:border-0">
                {message}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
