# KESMA Advertisement Automatizer

A Next.js application for managing advertisements across different portals. This tool automates the process of generating, adding, fetching, activating, and clearing advertisements.

## Features

- Generate advertisements from JSON templates
- Add advertisements to CMS portals
- Fetch existing advertisements from backend
- Activate advertisements
- Clear all advertisements
- Compare local and backend advertisement data

## Getting Started

### Prerequisites

- Node.js (version 18.18.0 or higher)
- npm or yarn

### Installation

1. Clone the repository:
   ```bash
   git clone https://gitlab.trendency.hu/kozponti-cms/kesma-advertisement-automatizer
   cd kesma-advertisement-automatizer
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```

3. Run the development server:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## How It Works

### Environment and Portal Selection

1. Select an environment (development, staging, production)
2. Select a portal to work with

### Advertisement Workflow

1. **Generate Ads**: Creates advertisements from JSON templates in the raw-zones folder
2. **Login**: Authenticate with the selected portal
3. **Fetch Ads**: Retrieve existing advertisements from the backend
4. **Add Ads**: Upload locally generated advertisements to the backend
5. **Activate Ads**: Enable advertisements on the backend
6. **Clear Ads**: Remove all advertisements from the backend

### Advertisement Table

The table displays a comparison between local JSON data and backend-stored advertisements:
- Shows matching status between local and backend data
- Highlights differences in masterID and ZoneID values
- Indicates when items are missing from the backend

## Configuration

- JSON templates are stored in the `raw-zones` folder
- Portal configurations are defined in `app/constants/app.consts.ts`

## Performance Considerations

For large sets of advertisements:
- Use the chunk size option to process advertisements in batches
- Set delays between chunks to prevent API rate limiting
- Monitor the status messages for progress updates

## Tech Stack

- Next.js 15
- React 19
- TypeScript
- Shadcn UI Components
- Tailwind CSS
